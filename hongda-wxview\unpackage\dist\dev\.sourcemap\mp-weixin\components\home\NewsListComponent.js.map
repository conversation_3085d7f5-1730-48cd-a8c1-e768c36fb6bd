{"version": 3, "file": "NewsListComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9OZXdzTGlzdENvbXBvbmVudC52dWU"], "sourcesContent": ["<template>\r\n  <view v-if=\"articleList.length > 0\" class=\"news-list-container\">\r\n    <view class=\"section-header\">\r\n      <view class=\"title-wrapper\">\r\n        <text class=\"title-main\">{{ titleParts.main }}</text>\r\n        <text class=\"title-gradient\">{{ titleParts.gradient }}</text>\r\n      </view>\r\n      <view class=\"more-link\" @click=\"goToArticleListPage\">\r\n        <text>更多</text>\r\n        <u-icon name=\"arrow-right\" size=\"14\" color=\"#9B9A9A\"></u-icon>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"tabs-container\">\r\n      <u-tabs\r\n          :list=\"tagList\"\r\n          :current=\"currentTabIndex\"\r\n          keyName=\"name\"\r\n          @change=\"handleTabChange\"\r\n          lineColor=\"#023F98\"\r\n          :lineHeight=\"4\"\r\n          :lineWidth=\"40\"\r\n          :activeStyle=\"{\r\n            color: '#23232A',\r\n            fontSize: '30rpx',\r\n            fontWeight: 'bold'\r\n          }\"\r\n          :inactiveStyle=\"{\r\n            color: '#9B9A9A',\r\n            fontSize: '30rpx'\r\n          }\"\r\n      ></u-tabs>\r\n    </view>\r\n\r\n    <view class=\"article-list\">\r\n      <view class=\"article-card\" v-for=\"item in articleList\" :key=\"item.id\" @click=\"gotoDetail(item.id)\">\r\n        <view class=\"card-cover\">\r\n          <u-image\r\n              :src=\"getFullImageUrl(item.coverImageUrl)\"\r\n              width=\"336rpx\"\r\n              height=\"192rpx\"\r\n              radius=\"12rpx\"\r\n              :lazy-load=\"true\"\r\n          ></u-image>\r\n        </view>\r\n        <view class=\"card-content\">\r\n          <text class=\"card-title\">{{ item.title }}</text>\r\n          <view class=\"card-meta\">\r\n            <text class=\"meta-source\">{{ item.source }}</text>\r\n            <text class=\"meta-date\">{{ formatDate(item.publishTime) }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { getArticleList } from '@/api/content/article.js';\r\nimport { listAllTag } from '@/api/content/tag.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\n\r\n// --- Props ---\r\nconst props = defineProps({\r\n  title: {\r\n    type: String,\r\n    default: '为你推荐'\r\n  },\r\n  params: {\r\n    type: Object,\r\n    default: () => ({\r\n      pageNum: 1,\r\n      pageSize: 4,\r\n      orderByColumn: 'publish_time',\r\n      isAsc: 'desc'\r\n    })\r\n  }\r\n});\r\n\r\n// --- 组件状态 ---\r\nconst articleList = ref([]);\r\nconst tagList = ref([]);\r\nconst currentTabIndex = ref(0);\r\nconst queryParams = ref({ ...props.params });\r\n\r\n// --- Computed ---\r\nconst titleParts = computed(() => {\r\n  const title = props.title;\r\n  if (title.length > 2) {\r\n    return {\r\n      main: title.slice(0, 2),\r\n      gradient: title.slice(2)\r\n    };\r\n  }\r\n  return { main: title, gradient: '' };\r\n});\r\n\r\n// --- 方法 ---\r\nconst loadTags = async () => {\r\n  try {\r\n    const response = await listAllTag();\r\n    const backendTags = Array.isArray(response.data) ? response.data : [];\r\n    tagList.value = [{ id: null, name: '全部' }, ...backendTags.slice(0, 4)];\r\n  } catch (error) {\r\n    console.error('加载标签列表失败:', error);\r\n  }\r\n};\r\n\r\nconst loadArticles = async (isRefresh = false) => {\r\n  if (isRefresh) {\r\n    queryParams.value.pageNum = 1;\r\n  }\r\n  try {\r\n    const response = await getArticleList(queryParams.value);\r\n    articleList.value = response.rows;\r\n  } catch (error) {\r\n    console.error('加载资讯列表失败:', error);\r\n  }\r\n};\r\n\r\nconst handleTabChange = (tab) => {\r\n  currentTabIndex.value = tab.index;\r\n  queryParams.value.tagIds = tagList.value[tab.index].id;\r\n  loadArticles(true);\r\n};\r\n\r\nconst gotoDetail = (id) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_article/detail?id=${id}`\r\n  });\r\n};\r\n\r\nconst goToArticleListPage = () => {\r\n  uni.switchTab({\r\n    url: '/pages/article/index'\r\n  });\r\n};\r\n\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return '';\r\n  return dateString.split(' ')[0];\r\n};\r\n\r\nonMounted(() => {\r\n  loadTags();\r\n  loadArticles(true);\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.news-list-container {\r\n  margin-top: 2rpx;\r\n  background-color: #FFFFFF;\r\n  padding: 32rpx 24rpx;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #FFFFFF;\r\n  padding: 0;\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.title-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.title-main {\r\n  /* \"为你\"的样式 */\r\n  font-size: 40rpx;\r\n  font-weight: 400; \r\n  color: #023F98;\r\n  font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;\r\n}\r\n.title-gradient {\r\n  /* \"推荐\"的样式 */\r\n  font-size: 40rpx;\r\n  font-weight: 400;\r\n  background-image: linear-gradient(91.61deg, #FFAD22 0%, #FFBB87 100%);\r\n  -webkit-background-clip: text;\r\n  background-clip: text;\r\n  color: transparent;\r\n  font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;\r\n}\r\n.more-link {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 28rpx;\r\n  color: #909399;\r\n  cursor: pointer;\r\n  transition: color 0.2s ease;\r\n\r\n  &:active {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n.tabs-container {\r\n  position: relative;\r\n  background-color: #FFFFFF;\r\n  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.02), 0 -4rpx 10rpx rgba(0,0,0,0.02);\r\n  margin-left: -24rpx;\r\n  margin-right: -24rpx;\r\n  padding-left: 24rpx;\r\n  padding-right: 24rpx;\r\n  // 用伪元素创建真正铺满屏幕的横线\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: -100vw; // 延伸到屏幕外\r\n    right: -100vw; // 延伸到屏幕外\r\n    height: 1rpx;\r\n    background-color: #F0F2F5;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: -100vw; // 延伸到屏幕外\r\n    right: -100vw; // 延伸到屏幕外\r\n    height: 1rpx;\r\n    background-color: #F0F2F5;\r\n    z-index: 1;\r\n  }\r\n}\r\n\r\n.article-list {\r\n  margin: 0;\r\n}\r\n\r\n.article-card {\r\n  display: flex;\r\n  gap: 30rpx;\r\n  padding: 24rpx 0;\r\n  border-bottom: 1rpx solid #F0F2F5;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.card-cover {\r\n  flex-shrink: 0;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  background-color: #f3f4f6;\r\n}\r\n\r\n.card-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  min-width: 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #23232A;\r\n  line-height: 1.5;\r\n\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  line-clamp: 2; /* 兼容标准属性 */\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.card-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 16rpx;\r\n  font-size: 24rpx;\r\n  color: #9B9A9A;\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "computed", "listAllTag", "uni", "getArticleList", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA,UAAM,QAAQ;AAiBd,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AACtB,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,cAAcA,cAAAA,IAAI,EAAE,GAAG,MAAM,OAAQ,CAAA;AAG3C,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAChC,YAAM,QAAQ,MAAM;AACpB,UAAI,MAAM,SAAS,GAAG;AACpB,eAAO;AAAA,UACL,MAAM,MAAM,MAAM,GAAG,CAAC;AAAA,UACtB,UAAU,MAAM,MAAM,CAAC;AAAA,QAC7B;AAAA,MACG;AACD,aAAO,EAAE,MAAM,OAAO,UAAU,GAAE;AAAA,IACpC,CAAC;AAGD,UAAM,WAAW,YAAY;AAC3B,UAAI;AACF,cAAM,WAAW,MAAMC,gBAAAA;AACvB,cAAM,cAAc,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO;AACnE,gBAAQ,QAAQ,CAAC,EAAE,IAAI,MAAM,MAAM,KAAM,GAAE,GAAG,YAAY,MAAM,GAAG,CAAC,CAAC;AAAA,MACtE,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,gDAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAEA,UAAM,eAAe,OAAO,YAAY,UAAU;AAChD,UAAI,WAAW;AACb,oBAAY,MAAM,UAAU;AAAA,MAC7B;AACD,UAAI;AACF,cAAM,WAAW,MAAMC,oBAAAA,eAAe,YAAY,KAAK;AACvD,oBAAY,QAAQ,SAAS;AAAA,MAC9B,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,gDAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,QAAQ;AAC/B,sBAAgB,QAAQ,IAAI;AAC5B,kBAAY,MAAM,SAAS,QAAQ,MAAM,IAAI,KAAK,EAAE;AACpD,mBAAa,IAAI;AAAA,IACnB;AAEA,UAAM,aAAa,CAAC,OAAO;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,sCAAsC,EAAE;AAAA,MACjD,CAAG;AAAA,IACH;AAEA,UAAM,sBAAsB,MAAM;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,eAAe;AACjC,UAAI,CAAC;AAAY,eAAO;AACxB,aAAO,WAAW,MAAM,GAAG,EAAE,CAAC;AAAA,IAChC;AAEAE,kBAAAA,UAAU,MAAM;AACd;AACA,mBAAa,IAAI;AAAA,IACnB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClJD,GAAG,gBAAgB,SAAS;"}