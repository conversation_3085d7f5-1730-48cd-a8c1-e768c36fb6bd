"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "CommentItem",
  props: {
    comment: {
      type: Object,
      required: true
    }
  },
  emits: [],
  setup(__props, { emit: __emit }) {
    const assets = common_vendor.ref(common_vendor.index.getStorageSync("staticAssets") || {});
    const defaultAvatarUrl = common_vendor.computed(() => {
      return assets.value.default_avatar || "";
    });
    const formatDateTime = (time) => {
      if (!time)
        return "";
      const date = new Date(time);
      const Y = date.getFullYear();
      const M = (date.getMonth() + 1).toString().padStart(2, "0");
      const D = date.getDate().toString().padStart(2, "0");
      const h = date.getHours().toString().padStart(2, "0");
      const m = date.getMinutes().toString().padStart(2, "0");
      const s = date.getSeconds().toString().padStart(2, "0");
      return `${Y}-${M}-${D} ${h}:${m}:${s}`;
    };
    return (_ctx, _cache) => {
      return {
        a: __props.comment.avatarUrl || defaultAvatarUrl.value,
        b: common_vendor.t(__props.comment.nickname || "匿名用户"),
        c: common_vendor.t(formatDateTime(__props.comment.createTime)),
        d: common_vendor.t(__props.comment.content)
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c3ec56f9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/CommentItem.js.map
