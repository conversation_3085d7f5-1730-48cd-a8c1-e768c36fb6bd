"use strict";
const tagStyle = {
  p: "line-height: 1.75; margin: 32rpx 0; color: #3D424D; font-size: 30rpx;",
  h1: "font-size: 44rpx; font-weight: 700; margin: 40rpx 0 20rpx 0; color: #303133;",
  h2: "font-size: 40rpx; font-weight: 700; margin: 36rpx 0 20rpx 0; color: #303133; border-bottom: 2rpx solid #e9e9eb; padding-bottom: 10rpx;",
  h3: "font-size: 36rpx; font-weight: 700; margin: 30rpx 0 16rpx 0; color: #303133; border-left: 8rpx solid #3c9cff; padding-left: 20rpx;",
  blockquote: "border-left: 6rpx solid #dcdfe6; padding: 20rpx 30rpx; margin: 30rpx 0; background: #f8f9fa; color: #606266; font-style: italic;",
  pre: 'background: #f8f9fa; color: #303133; padding: 30rpx; margin: 30rpx 0; border-radius: 16rpx; font-family: "Fira Code", monospace; overflow-x: auto; border: 2rpx solid #e9e9eb;',
  code: 'font-family: "Fira Code", monospace; background: #e9e9eb; color: #e53e3e; padding: 4rpx 10rpx; border-radius: 8rpx; font-size: 0.9em;',
  ul: "padding-left: 50rpx; margin: 30rpx 0;",
  ol: "padding-left: 50rpx; margin: 30rpx 0;",
  li: "margin-bottom: 16rpx; line-height: 1.7; color: #3D424D;",
  table: "width: 100% !important; border-collapse: collapse; margin: 40rpx 0; border-radius: 16rpx; overflow: hidden; box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.05);",
  th: "background: #f8f9fa; color: #303133; padding: 24rpx 30rpx; text-align: left; font-weight: 600; font-size: 28rpx; border: 2rpx solid #e9e9eb;",
  td: "border: 2rpx solid #e9e9eb; padding: 24rpx 30rpx; background: #ffffff; color: #3D424D; font-size: 28rpx;",
  img: "max-width: 100%; height: auto; border-radius: 16rpx; display: block; margin: 40rpx auto; box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);"
};
const containerStyle = `
table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 40rpx 0 !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05) !important;
  border: 2rpx solid #e9e9eb !important;
}
th {
  background: #f8f9fa !important;
  color: #303133 !important;
  padding: 24rpx !important;
  text-align: left !important;
  font-weight: 600 !important;
  font-size: 28rpx !important;
  border: 2rpx solid #e9e9eb !important;
}
td {
  border: 2rpx solid #e9e9eb !important;
  padding: 24rpx !important;
  background: #ffffff !important;
  color: #3D424D !important;
  font-size: 28rpx !important;
  vertical-align: middle !important;
}
thead {
  background: #f8f9fa !important;
}
tbody {
  background: #ffffff !important;
}
`;
exports.containerStyle = containerStyle;
exports.tagStyle = tagStyle;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/mpHtmlStyles.js.map
