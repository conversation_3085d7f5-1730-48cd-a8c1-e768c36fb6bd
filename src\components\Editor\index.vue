<template>
  <div :style="containerStyle">
    <Toolbar
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
        style="border-bottom: 1px solid #ccc"
    />
    <Editor
        :defaultConfig="editorConfig"
        :mode="mode"
        v-model="valueHtml"
        :style="styles"
        @onCreated="handleCreated"
        @onChange="handleChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, shallowRef, onBeforeUnmount } from 'vue';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { getToken } from "@/utils/auth";
import '@/assets/styles/editor-style.scss'

const props = defineProps({
  modelValue: { type: String, default: "" },
  width: { type: [Number, String], default: "auto" },
  height: { type: [Number, String], default: "300px" },
  readOnly: { type: Boolean, default: false },
});

const emit = defineEmits(['update:modelValue']);

const editorRef = shallowRef();
const valueHtml = ref(props.modelValue);
const mode = 'default';

const styles = computed(() => ({
  flex: 1,
  minHeight: '400px',
}));

const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  border: '1px solid #ccc',
  zIndex: 100,
  display: 'flex',
  flexDirection: 'column'
}));

const toolbarConfig = ref({});

const editorConfig = ref({
  placeholder: '请输入内容...',
  readOnly: props.readOnly,
  MENU_CONF: {
    uploadImage: {
      server: import.meta.env.VITE_APP_BASE_API + "/common/upload",
      fieldName: 'file',
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // [核心修改] 利用 insertFn 的第三个参数 href
      customInsert(res, insertFn) {
        if (res.code === 200 && res.url && res.objectName) {
          // src: 用于预览的临时URL
          // alt: 图片alt文本
          // href: 用来传递我们的 objectName，并添加一个特殊前缀用于后端识别
          insertFn(res.url, res.originalFilename, `oss-object-name://${res.objectName}`);
        } else {
          console.error("图片上传失败:", res.msg);
        }
      },
    },
    uploadVideo: {
      server: import.meta.env.VITE_APP_BASE_API + "/common/upload",
      fieldName: 'file',
      headers: {
        Authorization: "Bearer " + getToken()
      },
      maxFileSize: 50 * 1024 * 1024,
      customInsert(res, insertFn) {
        if (res.code === 200 && res.url) {
          insertFn(res.url);
        } else {
          console.error("视频上传失败:", res.msg);
        }
      },
    }
  }
});

const handleCreated = (editor) => {
  editorRef.value = editor;
};

const handleChange = (editor) => {
  emit('update:modelValue', editor.getHtml());
};

watch(() => props.modelValue, (newValue) => {
  if (newValue !== valueHtml.value) {
    valueHtml.value = newValue;
  }
});

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>