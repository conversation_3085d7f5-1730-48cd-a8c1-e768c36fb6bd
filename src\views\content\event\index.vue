<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
      <!-- 第一行：活动名称和活动地点 -->
      <el-form-item label="活动名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动地点" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入活动地点"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <!-- 第二行：报名状态、搜索和重置按钮 -->
      <el-form-item label="报名状态" prop="registrationStatus">
        <el-select
          v-model="queryParams.registrationStatus"
          placeholder="请选择报名状态"
          clearable
          style="width: 120px"
        >
          <el-option label="未开始" value="0" />
          <el-option label="报名中" value="1" />
          <el-option label="已结束" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['content:event:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['content:event:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['content:event:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['content:event:export']"
        >导出</el-button>
      </el-col>
      
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="eventList" 
      row-key="id" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动ID" align="center" prop="id" width="80" />
      <el-table-column label="活动名称" align="center" prop="title" show-overflow-tooltip />
      <!-- 报名情况列 -->
      <el-table-column label="报名情况" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.maxParticipants > 0">
            {{ scope.row.registeredCount || 0 }} / {{ scope.row.maxParticipants }}
          </span>
          <span v-else>
            {{ scope.row.registeredCount || 0 }} / 不限制
          </span>
        </template>
      </el-table-column>
      <el-table-column label="活动地点" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ formatEventLocation(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名状态" align="center" width="100">
         <template #default="scope">
            <el-tag :type="getRegistrationStatusType(scope.row.registrationStatus)">
              {{ getRegistrationStatusText(scope.row.registrationStatus) }}
            </el-tag>
         </template>
      </el-table-column>
      <el-table-column label="是否热门" align="center" prop="isHot" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isHot"
            :active-value="1"
            :inactive-value="0"
            @change="(value) => handleHotStatusChange(scope.row, value)"
            :loading="scope.row.hotStatusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column label="活动推广" align="center" width="100">
        <template #default="scope">
          <el-button 
            v-if="scope.row.isPromoted === 0" 
            type="primary" 
            size="small"
            @click="handlePromote(scope.row)"
            :loading="scope.row.promotionStatusLoading"
          >
            推广
          </el-button>
          <el-button 
            v-else 
            type="success" 
            size="small"
            @click="handleEditPromotion(scope.row)"
            :loading="scope.row.promotionStatusLoading"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="精选排序" align="center" width="150">
        <template #default="scope">
          <el-input-number
            v-if="scope.row.isHot === 1"
            v-model="scope.row.sortOrder"
            :min="0"
            :controls="false"
            controls-position="right"
            style="width: 100px"
            @change="handleSortOrderChange(scope.row)"
          />
          <!-- 非热门活动显示横杠 -->
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-dropdown trigger="click">
            <el-button link type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  icon="View" 
                  @click="handleViewRegistrations(scope.row)" 
                  v-hasPermi="['content:event:query']">
                  查看报名
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Edit" 
                  @click="handleUpdate(scope.row)" 
                  v-hasPermi="['content:event:edit']">
                  修改
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Delete" 
                  @click="handleDelete(scope.row)" 
                  v-hasPermi="['content:event:remove']"
                  :style="{ color: 'var(--el-color-danger)' }">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 活动表单对话框组件 -->
    <EventFormDialog ref="eventDialogRef" @success="getList" />

    <!-- 报名列表对话框组件 -->
    <RegistrationListDialog ref="registrationDialogRef" />

    <!-- 推广配置对话框-->
    <el-dialog :title="promotionDialogTitle" v-model="promotionDialogOpen" width="500px" append-to-body>
      <el-form ref="promotionFormRef" :model="promotionForm" :rules="promotionRules" label-width="120px">
        <el-form-item label="推广图片" prop="promotionImageUrl">
          <image-upload v-model="promotionForm.promotionImageUrl" recommendation-text="推荐大小为909px * 300px" :limit="1"/>
        </el-form-item>
        
        <el-form-item label="推广排序" prop="promotionSortOrder">
          <el-input-number 
            v-model="promotionForm.promotionSortOrder" 
            :min="0" 
            controls-position="right" 
            style="width: 100%" 
            placeholder="数字越小排序越靠前"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <!-- 取消推广按钮（仅编辑模式显示） -->
          <el-button 
            v-if="isEditMode" 
            type="danger" 
            @click="handleCancelPromotion"
            style="float: left"
          >
            取消推广
          </el-button>
          
          <el-button type="primary" @click="submitPromotionForm">确 定</el-button>
          <el-button @click="cancelPromotionForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>




  </div>
</template>

<script setup name="Event">
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();

// 图标导入
import { User } from '@element-plus/icons-vue';

// API导入
import { listEvent, delEvent, changeEventHotStatus, changeEventPromotionStatus, changeEventSortOrder, cancelEventPromotion } from "@/api/content/event";

// 组件导入
import EventFormDialog from '@/components/Event/EventFormDialog.vue';
import RegistrationListDialog from '@/components/Event/RegistrationListDialog.vue';

// 字典
const { hongda_event_status } = proxy.useDict('hongda_event_status');

// 组件引用
const eventDialogRef = ref(null);
const registrationDialogRef = ref(null);

/**
 * 格式化活动地点 - 后台管理页面显示完整省市信息
 * @param {Object} event 活动对象
 * @returns {String} 格式化后的地点信息
 */
const formatEventLocation = (event) => {
  // 直辖市处理：如果省市相同，只显示一个
  if (event.province && event.city) {
    // 判断是否为直辖市（省市名称相同或相似）
    const isDirectMunicipality = 
      event.province === event.city || 
      event.province.replace(/市$/, '') === event.city.replace(/市$/, '');
    
    if (isDirectMunicipality) {
      // 直辖市：只显示省份名称（保留"市"字）
      return event.province;
    } else {
      // 普通省市：显示省+市
      return `${event.province}${event.city}`;
    }
  }
  
  // 如果只有省份，直接使用
  if (event.province && event.province.trim()) {
    return event.province.trim();
  }
  
  // 如果只有city字段，直接使用
  if (event.city && event.city.trim()) {
    return event.city.trim();
  }
  
  // 如果只有完整地址，尝试提取省市信息
  if (event.location && event.location.trim()) {
    const location = event.location.trim();
    // 简单的省市提取逻辑，匹配常见的省市格式
    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);
    if (match) {
      const province = match[1];
      const city = match[2];
      if (city && province !== city) {
        return `${province}${city}`;
      }
      return province;
    }
  }
  
  return '待定';
};

// 基础状态
const eventList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 推广相关数据
const promotionDialogOpen = ref(false);
const promotionDialogTitle = ref("");
const currentPromotionEvent = ref(null);
const isEditMode = ref(false); // 标识是否为编辑模式

const promotionForm = reactive({
  id: null,
  isPromoted: 0,
  promotionImageUrl: '',
  promotionSortOrder: 0
});

const promotionRules = {
  promotionImageUrl: [
    { required: true, message: "推广图片不能为空", trigger: "blur" }
  ]
};

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    location: null,
    registrationStatus: null,
  }
});

const { queryParams } = toRefs(data);





// 通用API调用函数
const apiCall = async (apiFunc, params = null) => {
  try {
    return await apiFunc(params);
  } catch (error) {
    proxy.$modal.msgError(error.message || "操作失败");
    throw error;
  }
};





// 事件处理方法
function getList() {
  loading.value = true;

  // 创建一个不包含前端筛选参数的查询对象，用于获取所有数据进行前端分页
  const backendQuery = {
    pageNum: 1,
    pageSize: 9999, // 获取所有数据
    title: queryParams.value.title,
    location: queryParams.value.location
  };

  apiCall(listEvent, backendQuery).then(response => {
    let allRows = response.rows || [];

    // 前端筛选：按报名状态过滤
    if (queryParams.value.registrationStatus !== null && queryParams.value.registrationStatus !== '') {
      const targetRegistrationStatus = parseInt(queryParams.value.registrationStatus);
      allRows = allRows.filter(event => {
        return event.registrationStatus === targetRegistrationStatus;
      });
    }

    // 前端分页
    const pageSize = queryParams.value.pageSize;
    const pageNum = queryParams.value.pageNum;
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    eventList.value = allRows.slice(startIndex, endIndex);
    total.value = allRows.length; // 筛选后的总数
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}





function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  eventDialogRef.value.openDialog();
}

function handleUpdate(row) {
  const eventId = row?.id || ids.value[0];
  eventDialogRef.value.openDialog(eventId);
}



function handleDelete(row) {
  const _ids = row?.id || ids.value;
  proxy.$modal.confirm('是否确认删除活动管理编号为"' + _ids + '"的数据项？').then(() => {
    return apiCall(delEvent, _ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('content/event/export', {
    ...queryParams.value
  }, `event_${new Date().getTime()}.xlsx`);
}





function handleViewRegistrations(row) {
  registrationDialogRef.value.openDialog(row);
}



async function handleHotStatusChange(row, newValue) {
  const statusText = newValue === 1 ? '热门' : '普通';
  
  try {
    await proxy.$modal.confirm(`确认将活动"${row.title}"设置为${statusText}活动？`, "状态变更", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
        
    row.hotStatusLoading = true;
    
    await apiCall(changeEventHotStatus, {
      id: row.id,
      isHot: newValue
    });
    
    proxy.$modal.msgSuccess(`活动状态已更新为${statusText}活动`);
    
  } catch (error) {
    if (error === 'cancel') {
      row.isHot = row.isHot === 1 ? 0 : 1;
    } else {
      row.isHot = row.isHot === 1 ? 0 : 1;
      proxy.$modal.msgError("状态更新失败，请稍后重试");
    }
  } finally {
    row.hotStatusLoading = false;
  }
}

/**
 * 处理推广操作
 */
function handlePromote(row) {
  // 推广配置
  currentPromotionEvent.value = row;
  promotionDialogTitle.value = "配置活动推广";
  isEditMode.value = false;
  
  promotionForm.id = row.id;
  promotionForm.isPromoted = 1;
  promotionForm.promotionImageUrl = row.coverImageUrl || '';
  promotionForm.promotionSortOrder = 0;
  
  promotionDialogOpen.value = true;
}

/**
 * 处理编辑推广
 */
function handleEditPromotion(row) {
  // 编辑推广，显示配置对话框
  currentPromotionEvent.value = row;
  promotionDialogTitle.value = "编辑推广配置";
  isEditMode.value = true;
  
  // 使用当前推广配置
  promotionForm.id = row.id;
  promotionForm.isPromoted = 1;
  promotionForm.promotionImageUrl = row.promotionImageUrl || '';
  promotionForm.promotionSortOrder = row.promotionSortOrder || 0;
  
  promotionDialogOpen.value = true;
}

/**
 * 提交推广配置表单
 */
function submitPromotionForm() {
  proxy.$refs["promotionFormRef"].validate(valid => {
    if (valid) {
      submitPromotionStatusChange(promotionForm.id, promotionForm);
    }
  });
}

/**
 * 提交推广状态变更
 */
function submitPromotionStatusChange(eventId, formData) {
  const targetRow = eventList.value.find(item => item.id === eventId);
  if (targetRow) {
    targetRow.promotionStatusLoading = true;
  }
  
  changeEventPromotionStatus(formData).then(response => {
    proxy.$modal.msgSuccess(formData.isPromoted === 1 ? "推广开启成功" : "推广关闭成功");
    promotionDialogOpen.value = false;
    getList(); // 刷新列表
  }).catch(error => {
    proxy.$modal.msgError("操作失败：" + error.message);
    // 恢复开关状态
    if (targetRow) {
      targetRow.isPromoted = targetRow.isPromoted === 1 ? 0 : 1;
    }
  }).finally(() => {
    if (targetRow) {
      targetRow.promotionStatusLoading = false;
    }
  });
}

/**
 * 取消推广配置
 */
function cancelPromotionForm() {
  promotionDialogOpen.value = false;
  currentPromotionEvent.value = null;
  isEditMode.value = false;
}

/**
 * 处理取消推广
 */
async function handleCancelPromotion() {
  try {
    await proxy.$modal.confirm('确认取消该活动的推广功能吗？', "取消推广", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    
    if (currentPromotionEvent.value) {
      currentPromotionEvent.value.promotionStatusLoading = true;
    }
    
    await apiCall(cancelEventPromotion, currentPromotionEvent.value.id);
    
    proxy.$modal.msgSuccess("推广已取消");
    promotionDialogOpen.value = false;
    getList(); // 刷新列表
    
  } catch (error) {
    if (error !== 'cancel') {
      proxy.$modal.msgError("取消推广失败，请稍后重试");
    }
  } finally {
    if (currentPromotionEvent.value) {
      currentPromotionEvent.value.promotionStatusLoading = false;
    }
  }
}



/**
 * 获取报名状态标签类型
 */
const getRegistrationStatusType = (status) => {
  switch (status) {
    case 0: return 'warning';  // 未开始 - 橙色
    case 1: return 'success';  // 报名中 - 绿色
    case 2: return 'info';     // 已结束 - 灰色
    default: return 'info';
  }
};

/**
 * 获取报名状态文本
 */
const getRegistrationStatusText = (status) => {
  switch (status) {
    case 0: return '未开始';
    case 1: return '报名中';
    case 2: return '已结束';
    default: return '未知';
  }
};

/**
 * 处理精选排序值变化的函数
 * @param {object} row - 当前操作的行数据
 */
async function handleSortOrderChange(row) {
  // 临时存储修改前的值，用于失败时回滚UI
  const originalSortOrder = row.sortOrder;

  try {
    await changeEventSortOrder({
      id: row.id,
      sortOrder: row.sortOrder
    });
    // 成功反馈
    proxy.$modal.msgSuccess("排序更新成功");
    
    // 如果希望立即看到排序效果，可以重新获取列表
    // getList();

  } catch (error) {
    // 失败反馈
    proxy.$modal.msgError("更新失败，请重试");
    
    // 关键：将界面的值恢复到修改前的状态，保持UI与数据一致
    row.sortOrder = originalSortOrder;
  }
}



// 初始化
getList();
</script>

<style scoped>
/* 主组件样式，保持简洁 */
</style>
