<template>
  <div class="comment-item">
    <!-- 【简化】移除连接线和层级样式，统一为平级评论卡片 -->
    <div class="comment-card">
      <!-- 选择框 -->
      <div class="comment-select">
        <el-checkbox :model-value="selectedMap[comment.id]"
                     @change="(val) => emit('select', comment.id, val)"></el-checkbox>
      </div>

      <!-- 评论主体 -->
      <div class="comment-body">
        <!-- 用户信息和时间 -->
        <div class="comment-header">
          <div class="user-info">
            <el-avatar :size="32" :src="comment.avatarUrl || undefined">
              <el-icon>
                <User/>
              </el-icon>
            </el-avatar>
            <div class="user-details">
              <span class="username">{{ comment.nickname || '匿名用户' }}</span>
              <span class="comment-time">{{ parseTime(comment.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </div>
          </div>
          <div class="comment-status">
            <el-switch
                :model-value="comment.status"
                :active-value="1"
                :inactive-value="2"
                @change="(newStatus) => emit('status-change', comment, newStatus)"
                v-hasPermi="['data:comment:edit']"
            />
          </div>
        </div>

        <!-- 关联内容信息 -->
        <div class="comment-related">
          <dict-tag :options="dictRelatedType" :value="comment.relatedType"/>
          <span class="related-title" :title="comment.relatedTitle">{{ comment.relatedTitle }}</span>
        </div>

        <!-- 评论内容 -->
        <div class="comment-content">
          {{ comment.content }}
        </div>

        <!-- 【简化】操作按钮，移除回复和展开功能 -->
        <div class="comment-actions">
          <el-button link type="primary" size="small" @click="emit('edit', comment)" v-hasPermi="['data:comment:edit']">
            <el-icon>
              <Edit/>
            </el-icon>
            修改
          </el-button>
          <el-button link type="danger" size="small" @click="emit('delete', comment)"
                     v-hasPermi="['data:comment:remove']">
            <el-icon>
              <Delete/>
            </el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 【移除】子评论部分，不再支持嵌套结构 -->
  </div>
</template>

<script setup>
import {getCurrentInstance} from 'vue';
import {Delete, Edit, User} from '@element-plus/icons-vue';

const {proxy} = getCurrentInstance();
const {parseTime} = proxy;

const props = defineProps({
  comment: Object,
  selectedMap: Object,
  dictRelatedType: Array,
  dictStatus: Array,
});

const emit = defineEmits(['select', 'edit', 'delete', 'status-change']);


// 【移除】所有层级相关的样式和逻辑函数，简化为平级评论
</script>

<style scoped lang="scss">
.comment-item {
  position: relative;
}

.comment-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  border-left: 4px solid #409eff;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  display: flex;
  padding: 16px;
  background-color: #ffffff;
}

.comment-card:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.comment-select {
  margin-right: 12px;
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
}

.comment-body {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-related {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f5f7fa;
  padding: 6px 10px;
  border-radius: 4px;
}

.related-title {
  font-size: 13px;
  color: #606266;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-content {
  color: #303133;
  line-height: 1.7;
  margin-bottom: 12px;
  word-break: break-word;
  font-size: 14px;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  border-top: 1px solid #f0f2f5;
  padding-top: 12px;
}

// 【移除】子评论相关样式，不再需要

@media (max-width: 768px) {
  .comment-card {
    padding: 12px;
  }
  .user-info {
    gap: 8px;
  }
  .comment-actions {
    gap: 8px;
    flex-wrap: wrap;
  }
  .comment-content {
    font-size: 13px;
  }
  .related-title {
    max-width: 180px;
  }
}

// 【移除】不再需要的动画效果
</style>