"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const utils_image = require("../../utils/image.js");
const utils_location = require("../../utils/location.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_loading_icon2 = common_vendor.resolveComponent("up-loading-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_up_icon2 + _easycom_up_loading_icon2 + _easycom_up_empty2)();
}
const _easycom_up_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_up_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_loading_icon + _easycom_up_empty)();
}
const INITIAL_COUNT = 4;
const STEP_COUNT = 2;
const MAX_FETCH_COUNT = 100;
const _sfc_main = {
  __name: "ActivityGridComponent",
  emits: ["all-loaded-change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emit = __emit;
    const icons = {
      time: "",
      location: ""
    };
    const isLoading = common_vendor.ref(false);
    const isLoadingMore = common_vendor.ref(false);
    const allEvents = common_vendor.ref([]);
    const visibleCount = common_vendor.ref(0);
    const titleParts = common_vendor.computed(() => {
      const title = "精选活动";
      return { main: title.slice(0, 2), gradient: title.slice(2) };
    });
    const activityList = common_vendor.computed(() => {
      return allEvents.value.slice(0, Math.min(visibleCount.value, allEvents.value.length));
    });
    const hasMore = common_vendor.computed(() => visibleCount.value < allEvents.value.length);
    common_vendor.watch(hasMore, (newVal) => {
      emit("all-loaded-change", !newVal);
    });
    common_vendor.onMounted(() => {
      try {
        const assets = common_vendor.index.getStorageSync("staticAssets");
        icons.time = (assets == null ? void 0 : assets.detail_icon_time) || "";
        icons.location = (assets == null ? void 0 : assets.detail_icon_location) || "";
      } catch (e) {
      }
      fetchHotEvents();
    });
    const parseSafeDate = (input) => {
      if (!input)
        return null;
      if (input instanceof Date) {
        return isNaN(input.getTime()) ? null : input;
      }
      if (typeof input === "number") {
        const d = new Date(input);
        return isNaN(d.getTime()) ? null : d;
      }
      if (typeof input === "string") {
        let s = input.trim();
        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(s)) {
          s = s.replace(" ", "T");
        }
        let d = new Date(s);
        if (isNaN(d.getTime())) {
          const m = s.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);
          if (m) {
            const y = m[1];
            const mo = m[2];
            const day = m[3];
            const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || "00"}` : "";
            d = /* @__PURE__ */ new Date(`${y}/${mo}/${day}${rest}`);
          }
        }
        return isNaN(d.getTime()) ? null : d;
      }
      return null;
    };
    const formatActivityDate = (dateString) => {
      if (!dateString)
        return "";
      const date = parseSafeDate(dateString);
      if (!date)
        return "";
      const month = date.getMonth() + 1;
      const day = String(date.getDate()).padStart(2, "0");
      return `${month}.${day}`;
    };
    const getWeekday = (dateString) => {
      if (!dateString)
        return "";
      const date = parseSafeDate(dateString);
      if (!date)
        return "";
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      return weekdays[date.getDay()];
    };
    const calculateRemaining = (maxParticipants, registeredCount) => {
      if (!maxParticipants || maxParticipants <= 0) {
        return "不限";
      }
      const remaining = maxParticipants - (registeredCount || 0);
      return remaining > 0 ? remaining : 0;
    };
    const fetchHotEvents = async () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      try {
        const response = await api_data_event.getHotEventListApi(MAX_FETCH_COUNT);
        const events = response.data || response.rows || [];
        const registeringRows = events.filter((item) => item.registrationStatus === 1);
        allEvents.value = registeringRows.map((item) => ({
          id: item.id,
          title: item.title,
          image: utils_image.getFullImageUrl(item.coverImageUrl),
          location: utils_location.formatActivityLocation(item),
          status: item.registrationStatusText || "报名中",
          statusClass: "status-registering",
          dateTime: formatActivityDate(item.startTime),
          weekday: getWeekday(item.startTime),
          remainingSpots: calculateRemaining(item.maxParticipants, item.registeredCount)
        }));
        visibleCount.value = Math.min(INITIAL_COUNT, allEvents.value.length);
        emit("all-loaded-change", !(visibleCount.value < allEvents.value.length));
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取热门活动失败:", error);
        allEvents.value = [
          {
            id: 1,
            title: "2025 Ozen卖家增长峰会·北京站",
            status: "报名中",
            statusClass: "status-registering",
            location: "北京",
            image: "https://via.placeholder.com/170x100/3c9cff/fff?text=活动1",
            dateTime: "7.15",
            weekday: "周二",
            remainingSpots: "29"
          }
        ];
        visibleCount.value = Math.min(INITIAL_COUNT, allEvents.value.length);
        emit("all-loaded-change", !(visibleCount.value < allEvents.value.length));
        common_vendor.index.showToast({
          title: "获取活动数据失败，显示示例数据",
          icon: "none",
          duration: 2e3
        });
      } finally {
        isLoading.value = false;
      }
    };
    const loadMore = () => {
      if (isLoadingMore.value)
        return;
      if (!hasMore.value)
        return;
      isLoadingMore.value = true;
      setTimeout(() => {
        visibleCount.value = Math.min(visibleCount.value + STEP_COUNT, allEvents.value.length);
        isLoadingMore.value = false;
      }, 50);
    };
    const goToEventDetail = (activity) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${activity.id}`
      });
    };
    const goToEventList = () => {
      common_vendor.index.switchTab({
        url: "/pages/event/index",
        fail: () => {
          common_vendor.index.navigateTo({ url: "/pages/event/index" });
        }
      });
    };
    __expose({ loadMore });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(titleParts.value.main),
        b: common_vendor.t(titleParts.value.gradient),
        c: common_vendor.p({
          name: "arrow-right",
          size: "14"
        }),
        d: common_vendor.o(goToEventList),
        e: isLoading.value
      }, isLoading.value ? {
        f: common_vendor.p({
          mode: "spinner",
          size: "40"
        })
      } : {
        g: common_vendor.f(activityList.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.status),
            c: common_vendor.n(item.statusClass),
            d: common_vendor.t(item.title),
            e: common_vendor.t(item.dateTime),
            f: common_vendor.t(item.weekday),
            g: common_vendor.t(item.location),
            h: common_vendor.t(item.remainingSpots),
            i: item.id || index,
            j: common_vendor.o(($event) => goToEventDetail(item), item.id || index)
          };
        }),
        h: icons.time,
        i: icons.location
      }, {
        j: !isLoading.value && activityList.value.length === 0
      }, !isLoading.value && activityList.value.length === 0 ? {
        k: common_vendor.p({
          mode: "data",
          text: "暂无精选活动",
          textColor: "#909399",
          iconSize: "80"
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-03377cb7"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
