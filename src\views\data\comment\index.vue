<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="关联类型" prop="relatedType" style="width: 300px">
        <el-select v-model="queryParams.relatedType" placeholder="请选择关联类型" clearable>
          <el-option
              v-for="dict in hongda_comment_related_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="用户" prop="userId">
        <el-select
            v-model="queryParams.userId"
            placeholder="请输入用户昵称进行搜索"
            filterable
            remote
            clearable
            :remote-method="remoteUserSearch"
            :loading="userLoading"
            style="width: 240px"
        >
          <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="关联内容" prop="relatedTitle">
        <el-input
            v-model="queryParams.relatedTitle"
            placeholder="请输入关联内容的标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status" style="width: 200px">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
              v-for="dict in hongda_comment_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['data:comment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete()"
            v-hasPermi="['data:comment:remove']"
        >删除</el-button>
      </el-col>
      <!-- 【移除】展开/收起按钮，不再需要树形结构 -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 【简化】平级评论列表，移除树形结构 -->
    <div class="comment-container" v-loading="loading">
      <div v-if="commentList && commentList.length > 0">
        <div v-for="comment in commentList" :key="comment.id" class="comment-item">
          <CommentItem
              :comment="comment"
              :selected-map="selectedMap"
              :dict-related-type="hongda_comment_related_type"
              :dict-status="hongda_comment_status"
              @select="handleCommentSelect"
              @edit="handleUpdate"
              @delete="handleDelete"
              @status-change="handleStatusChange"
          />
        </div>
      </div>
      <el-empty v-else description="暂无评论数据" />
    </div>

    <!-- 添加或修改评论管理对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="commentRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="发表用户" prop="userId">
          <el-select
              v-model="form.userId"
              placeholder="请输入用户昵称进行搜索"
              filterable
              remote
              clearable
              :remote-method="remoteDialogUserSearch"
              :loading="dialogUserLoading"
              style="width: 100%"
          >
            <el-option
                v-for="item in dialogUserList"
                :key="item.id"
                :label="item.nickname + ' (ID:' + item.id + ')'"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联类型" prop="relatedType">
          <el-select v-model="form.relatedType" placeholder="请选择关联类型">
            <el-option
                v-for="dict in hongda_comment_related_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联的内容ID" prop="relatedId">
          <el-input v-model="form.relatedId" placeholder="请输入关联的内容ID" />
        </el-form-item>
        <!-- 【移除】父评论ID字段，不再支持回复功能 -->
        <el-form-item label="评论内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入内容" :rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Comment">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import { listComment, getComment, delComment, addComment, updateComment } from "@/api/data/comment";
import CommentItem from './CommentItem.vue';
import {listMiniuser} from "@/api/data/miniuser.js";

const { proxy } = getCurrentInstance();
const { hongda_comment_related_type, hongda_comment_status } = proxy.useDict('hongda_comment_related_type', 'hongda_comment_status');

const commentList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const multiple = ref(true);
const title = ref("");
// 【移除】isReply和expandedMap，简化为平级评论
const selectedMap = reactive({}); // 选择状态管理

const userList = ref([]);
const userLoading = ref(false);

const dialogUserList = ref([]);
const dialogUserLoading = ref(false);

const remoteDialogUserSearch = (query) => {
  if (query) {
    dialogUserLoading.value = true;
    listMiniuser({ nickname: query }).then(response => {
      dialogUserList.value = response.rows;
      dialogUserLoading.value = false;
    });
  } else {
    dialogUserList.value = [];
  }
};

// 添加远程搜索方法
const remoteUserSearch = (query) => {
  if (query) {
    userLoading.value = true;
    // 将用户输入的内容作为 nickname 参数，调用接口
    listMiniuser({ nickname: query }).then(response => {
      userList.value = response.rows;
      userLoading.value = false;
    });
  } else {
    // 如果输入为空，则清空列表
    userList.value = [];
  }
};

// 【移除】展开/收起相关逻辑，不再需要树形结构

const data = reactive({
  form: {},
  queryParams: {
    userId: null,
    relatedType: null,
    // relatedId: null, // 不再使用此字段进行内容搜索
    status: null,
    // [新增] 用于关联内容标题的搜索
    relatedTitle: null
  },
  rules: {
    userId: [{ required: true, message: "发表用户ID不能为空", trigger: "blur" }],
    relatedType: [{ required: true, message: "关联类型不能为空", trigger: "change" }],
    relatedId: [{ required: true, message: "关联的内容ID不能为空", trigger: "blur" }],
    content: [{ required: true, message: "评论内容不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询评论管理列表 */
function getList() {
  loading.value = true;
  listComment(queryParams.value).then(response => {
    // 【简化】直接获取平级评论列表
    commentList.value = response.data;
    loading.value = false;
  });
}

// 【简化】评论选择处理，移除树形遍历逻辑
function handleCommentSelect(commentId, isSelected) {
  selectedMap[commentId] = isSelected;
  updateSelectionState();
}

// 更新选择状态
function updateSelectionState() {
  const selectedIds = Object.keys(selectedMap).filter(id => selectedMap[id]);
  ids.value = selectedIds;
  multiple.value = selectedIds.length === 0;
}


// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    relatedType: null,
    relatedId: null,
    content: null,
    status: 1, // 默认显示状态
  };
  // 【移除】isReply相关逻辑
  dialogUserList.value = [];
  proxy.resetForm("commentRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评论";
}

// 【移除】回复功能，不再支持回复操作

/** 修改按钮操作 */
function handleUpdate(comment) {
  reset();
  const _id = comment.id;
  getComment(_id).then(response => {
    form.value = response.data;
    // 【新增】为了在弹窗中能回显用户名，需要手动查询一次该用户信息并放入列表
    if (form.value.userId) {
      listMiniuser({ id: form.value.userId }).then(userResponse => {
        dialogUserList.value = userResponse.rows;
      });
    }
    open.value = true;
    title.value = "修改评论";
  });
}


/** 提交按钮 */
function submitForm() {
  proxy.$refs["commentRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateComment(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addComment(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(comment) {
  const _ids = comment ? [comment.id] : ids.value;
  if (_ids.length === 0) {
    proxy.$modal.msgWarning("请选择要删除的评论");
    return;
  }
  proxy.$modal.confirm('是否确认删除选中的评论？此操作会一并删除其所有子评论。').then(function() {
    return delComment(_ids);
  }).then(() => {
    // 清空已删除的选择项
    _ids.forEach(id => {
      delete selectedMap[id];
    });
    updateSelectionState();
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 处理评论状态修改 */
function handleStatusChange(comment, newStatus) {
  const text = newStatus === 1 ? "显示" : "隐藏";
  proxy.$modal.confirm(`确认要将这条评论设置为"${text}"状态吗？`).then(function() {
    return updateComment({ id: comment.id, status: newStatus });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    // 直接更新列表中的数据，避免重新查询，体验更好
    comment.status = newStatus;
  }).catch(function() {
    // API调用失败时，无需手动恢复，因为数据未改变
    getList(); // 也可以选择刷新列表来同步正确状态
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.comment-container {
  background: #f0f2f5;
  border-radius: 8px;
  padding: 16px;
}
.comment-item {
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>