{"version": 3, "file": "QuickNavigation.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9RdWlja05hdmlnYXRpb24udnVl"], "sourcesContent": ["<template>\r\n  <view v-if=\"navList.length > 0\" class=\"nav-container\">\r\n    <view class=\"nav-grid\">\r\n      <view\r\n          v-for=\"item in navList\"\r\n          :key=\"item.id\"\r\n          class=\"nav-item\"\r\n          @click=\"handleNavClick(item)\"\r\n      >\r\n        <view class=\"nav-icon-wrapper\">\r\n          <image class=\"nav-icon\" :src=\"item.iconUrl\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <text class=\"nav-text\">{{ item.title }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { getNavListByPosition } from '@/api/platform/nav.js';\r\nimport { navigateTo } from '@/utils/navigation.js';\r\n\r\nconst navList = ref([]);\r\n\r\nconst fetchNavData = async () => {\r\n  try {\r\n    const res = await getNavListByPosition('HOME_QUICK_NAV');\r\n    if (res && res.data) {\r\n      navList.value = res.data;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取快捷导航失败:', error);\r\n  }\r\n};\r\n\r\nconst handleNavClick = (navItem) => {\r\n  navigateTo(navItem);\r\n};\r\n\r\nonMounted(() => {\r\n  fetchNavData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.nav-container {\r\n  width: 100%;\r\n  background-color: #ffffff;\r\n  padding: 32rpx 30rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.nav-grid {\r\n  display: grid;\r\n  /*\r\n   * [核心修改]\r\n   * 将 grid-template-columns 的 repeat(5, 1fr) 修改为 repeat(4, 1fr)\r\n   * 现在网格将固定为4列，自动撑满容器宽度。\r\n  */\r\n  grid-template-columns: repeat(4, 1fr);\r\n  row-gap: 36rpx;\r\n  // 适当加大了列间距，让4列布局更显疏朗\r\n  column-gap: 30rpx;\r\n}\r\n\r\n.nav-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  transition: transform 0.2s ease;\r\n\r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n.nav-icon-wrapper {\r\n  // 4列布局下，可以给图标更大的空间\r\n  width: 108rpx;\r\n  height: 108rpx;\r\n  border-radius: 32rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: hidden;\r\n  background-color: #f8fafc;\r\n}\r\n\r\n.nav-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.nav-text {\r\n  font-size: 24rpx;\r\n  color: #333333;\r\n  text-align: center;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "getNavListByPosition", "uni", "navigateTo", "onMounted"], "mappings": ";;;;;;;AAuBA,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AAEtB,UAAM,eAAe,YAAY;AAC/B,UAAI;AACF,cAAM,MAAM,MAAMC,sCAAqB,gBAAgB;AACvD,YAAI,OAAO,IAAI,MAAM;AACnB,kBAAQ,QAAQ,IAAI;AAAA,QACrB;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,6CAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,YAAY;AAClCC,uBAAU,WAAC,OAAO;AAAA,IACpB;AAEAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;ACzCD,GAAG,gBAAgB,SAAS;"}