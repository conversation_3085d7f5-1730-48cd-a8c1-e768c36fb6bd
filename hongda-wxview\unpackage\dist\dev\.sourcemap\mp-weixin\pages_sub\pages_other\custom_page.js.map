{"version": 3, "file": "custom_page.js", "sources": ["pages_sub/pages_other/custom_page.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXGN1c3RvbV9wYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"pageData && !loading\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <u-icon name=\"arrow-left\" color=\"#000000\" size=\"22\"></u-icon>\r\n        </view>\r\n        <view class=\"nav-title\">{{ pageData.title || '详情' }}</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n\r\n      <view class=\"main-content\">\r\n        <view class=\"article-title\">{{ pageData.title }}</view>\r\n\r\n        <view class=\"article-meta-new\">\r\n          <view class=\"meta-left\">\r\n            <text class=\"meta-text\">发布于 {{ formatDate(pageData.createTime, 'YYYY-MM-DD') }}</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"content-card\">\r\n          <view class=\"content-body\">\r\n            <mp-html :content=\"pageData.content\" :tag-style=\"tagStyle\" lazy-load/>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n\r\n  <u-loading-page v-else-if=\"loading\" loading-text=\"正在加载...\" :loading=\"loading\"></u-loading-page>\r\n\r\n  <view v-else class=\"error-state\">\r\n    <view class=\"empty-icon\">⚠️</view>\r\n    <text class=\"empty-title\">页面加载失败</text>\r\n    <text class=\"empty-desc\">无法获取页面内容，请稍后重试</text>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { getPageDetailsApi } from '@/pages_sub/pages_other/api/platform/page.js';\r\nimport { formatDate } from '@/utils/date.js';\r\n// [新增] 导入资讯详情页的富文本样式配置\r\nimport { tagStyle } from '@/utils/mpHtmlStyles.js';\r\n\r\nconst loading = ref(true);\r\nconst pageData = ref(null);\r\n\r\n// --- [新增] 自定义导航栏所需的状态和方法 ---\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    // 胶囊按钮在小程序中可用，H5等环境会报错，做兼容处理\r\n    // #ifdef MP-WEIXIN\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    navBarHeight.value = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight.value) * 2;\r\n    headerHeight.value = menuButtonInfo.bottom + (menuButtonInfo.top - statusBarHeight.value);\r\n    // #endif\r\n    // #ifndef MP-WEIXIN\r\n    navBarHeight.value = 44; // H5等其他环境给一个默认高度\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value;\r\n    // #endif\r\n  } catch (e) {\r\n    statusBarHeight.value = 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = 64;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n// --- 导航栏逻辑结束 ---\r\n\r\nonLoad(async (options) => {\r\n  getNavBarInfo(); // 初始化导航栏高度\r\n\r\n  const pageId = options.id;\r\n  if (!pageId) {\r\n    loading.value = false;\r\n    console.error(\"页面ID缺失\");\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const res = await getPageDetailsApi(pageId);\r\n    if (res.code === 200 && res.data) {\r\n      pageData.value = res.data;\r\n    } else {\r\n      pageData.value = null; // 确保在出错时清空数据\r\n    }\r\n  } catch (error) {\r\n    console.error(\"获取页面详情失败:\", error);\r\n    pageData.value = null;\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* ========== [核心修改] 复用资讯详情页的样式 ========== */\r\n\r\n.page-container {\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n/* 错误状态样式 */\r\n.error-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  .empty-icon { font-size: 80rpx; margin-bottom: 24rpx; opacity: 0.5; }\r\n  .empty-title { font-size: 30rpx; font-weight: 500; color: #606266; margin-bottom: 12rpx; }\r\n  .empty-desc { font-size: 26rpx; color: #909399; }\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #FFFFFF;\r\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.scrollable-content {\r\n  height: 100%;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 24rpx;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n  /* 标题过长时截断 */\r\n  max-width: 60vw;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 页面主内容区 */\r\n.main-content {\r\n  padding: 0;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.article-title {\r\n  font-size: 44rpx;\r\n  color: #23232A;\r\n  font-weight: 700;\r\n  line-height: 1.5;\r\n  padding: 40rpx 30rpx 0 30rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.article-meta-new {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  margin-bottom: 40rpx;\r\n\r\n  .meta-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 24rpx;\r\n  }\r\n\r\n  .meta-text {\r\n    font-size: 24rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n\r\n.content-card {\r\n  padding: 0 30rpx 40rpx 30rpx;\r\n}\r\n\r\n.content-body {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  line-height: 1.8;\r\n\r\n  // 注入到 mp-html 的样式由外部 js 文件控制，此处无需再写 :deep\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/custom_page.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onLoad", "getPageDetailsApi"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAiDA,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,WAAWA,cAAAA,IAAI,IAAI;AAGzB,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAE1B,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AACF,cAAM,aAAaC,oBAAI;AACvB,wBAAgB,QAAQ,WAAW,mBAAmB;AAGtD,cAAM,iBAAiBA,oBAAI;AAC3B,qBAAa,QAAQ,eAAe,UAAU,eAAe,MAAM,gBAAgB,SAAS;AAC5F,qBAAa,QAAQ,eAAe,UAAU,eAAe,MAAM,gBAAgB;AAAA,MAMpF,SAAQ,GAAG;AACV,wBAAgB,QAAQ;AACxB,qBAAa,QAAQ;AACrB,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa,EAAE,OAAO,EAAG,CAAA;AAAA,IAC/B;AAGAC,kBAAM,OAAC,OAAO,YAAY;AACxB;AAEA,YAAM,SAAS,QAAQ;AACvB,UAAI,CAAC,QAAQ;AACX,gBAAQ,QAAQ;AAChBD,sBAAAA,MAAA,MAAA,SAAA,+CAAc,QAAQ;AACtB;AAAA,MACD;AAED,UAAI;AACF,cAAM,MAAM,MAAME,0DAAkB,MAAM;AAC1C,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,mBAAS,QAAQ,IAAI;AAAA,QAC3B,OAAW;AACL,mBAAS,QAAQ;AAAA,QAClB;AAAA,MACF,SAAQ,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,gDAAA,aAAa,KAAK;AAChC,iBAAS,QAAQ;AAAA,MACrB,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzGD,GAAG,WAAW,eAAe;"}