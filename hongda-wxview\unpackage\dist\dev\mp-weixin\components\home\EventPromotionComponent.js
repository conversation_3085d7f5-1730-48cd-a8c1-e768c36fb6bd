"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_u_skeleton2 = common_vendor.resolveComponent("u-skeleton");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  (_easycom_u_skeleton2 + _easycom_up_button2)();
}
const _easycom_u_skeleton = () => "../../uni_modules/uview-plus/components/u-skeleton/u-skeleton.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_skeleton + _easycom_up_button)();
}
const _sfc_main = {
  __name: "EventPromotionComponent",
  setup(__props) {
    const flameIconUrl = common_vendor.ref("");
    const thumbUpIconUrl = common_vendor.ref("");
    const promoDataList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(true);
    const currentIndex = common_vendor.ref(0);
    const fetchPromoData = async () => {
      try {
        const response = await api_data_event.getPromotionEventListApi({
          pageSize: 10
        });
        if (response && response.code === 200) {
          let dataArray = null;
          if (response.rows && Array.isArray(response.rows)) {
            dataArray = response.rows;
          } else if (response.data && response.data.rows && Array.isArray(response.data.rows)) {
            dataArray = response.data.rows;
          }
          if (dataArray && Array.isArray(dataArray) && dataArray.length > 0) {
            promoDataList.value = dataArray.map((event) => {
              return {
                id: event.id,
                title: event.promotionTitle || event.title,
                // 优先使用推广标题
                image: utils_image.getFullImageUrl(event.promotionImageUrl || event.coverImageUrl),
                // 优先使用推广图片
                iconUrl: event.iconUrl,
                linkUrl: `/pages_sub/pages_event/detail?id=${event.id}`,
                // 直接跳转到活动详情
                // 使用活动的简介和卖点字段，如果为空则提供默认文案
                descriptionLine1: event.summary || "官方认证，品质保证",
                descriptionLine2: event.sellPoint || "干货满满，不容错过"
              };
            });
            common_vendor.index.__f__("log", "at components/home/<USER>", "获取推广活动成功，数量:", promoDataList.value.length);
          } else {
            promoDataList.value = [];
            common_vendor.index.__f__("warn", "at components/home/<USER>", "暂无推广活动数据");
          }
        } else {
          promoDataList.value = [];
          common_vendor.index.__f__("warn", "at components/home/<USER>", "获取推广活动失败，响应码:", response == null ? void 0 : response.code);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取推广数据失败:", error.message);
        promoDataList.value = [];
      } finally {
        isLoading.value = false;
      }
    };
    const onSwiperChange = (e) => {
      currentIndex.value = e.detail.current;
    };
    const switchToSlide = (index) => {
      currentIndex.value = index;
      common_vendor.index.__f__("log", "at components/home/<USER>", "点击指示器切换到索引:", index);
    };
    const handlePromoClick = (promoItem) => {
      if (!promoItem || !promoItem.id) {
        common_vendor.index.__f__("warn", "at components/home/<USER>", "推广卡片数据异常");
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${promoItem.id}`
      });
    };
    const onImageLoad = (e) => {
      common_vendor.index.__f__("log", "at components/home/<USER>", "图片加载成功");
    };
    const onImageError = (e) => {
      common_vendor.index.__f__("error", "at components/home/<USER>", "图片加载失败:", e);
    };
    common_vendor.onMounted(() => {
      try {
        const assets = common_vendor.index.getStorageSync("staticAssets");
        flameIconUrl.value = (assets == null ? void 0 : assets["flame-icon"]) || "";
        thumbUpIconUrl.value = (assets == null ? void 0 : assets["thumb-up-icon"]) || "";
      } catch (e) {
      }
      fetchPromoData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          loading: true,
          animate: true,
          rows: 4,
          title: true,
          titleWidth: "60%",
          rowsWidth: "['100%', '40%', '40%', '100%']",
          rowsHeight: "['180px', '20px', '20px', '40px']"
        })
      } : promoDataList.value.length > 0 ? common_vendor.e({
        d: common_vendor.f(promoDataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.iconUrl
          }, item.iconUrl ? {
            b: common_vendor.unref(utils_image.getFullImageUrl)(item.iconUrl)
          } : {}, {
            c: common_vendor.t(item.title),
            d: item.image,
            e: common_vendor.o(onImageError, item.id || index),
            f: common_vendor.o(onImageLoad, item.id || index),
            g: item.descriptionLine1
          }, item.descriptionLine1 ? {
            h: flameIconUrl.value,
            i: common_vendor.t(item.descriptionLine1)
          } : {}, {
            j: item.descriptionLine2
          }, item.descriptionLine2 ? {
            k: thumbUpIconUrl.value,
            l: common_vendor.t(item.descriptionLine2)
          } : {}, {
            m: common_vendor.o(($event) => handlePromoClick(item), item.id || index),
            n: "4eff421e-1-" + i0,
            o: common_vendor.o(($event) => handlePromoClick(item), item.id || index),
            p: item.id || index
          });
        }),
        e: common_vendor.p({
          type: "primary",
          shape: "square",
          text: "立即报名",
          size: "large",
          customStyle: {
            backgroundColor: "#023F98",
            height: "68rpx",
            width: "654rpx",
            borderRadius: "8rpx",
            margin: "0 auto"
          }
        }),
        f: currentIndex.value,
        g: common_vendor.o(onSwiperChange),
        h: promoDataList.value.length > 1
      }, promoDataList.value.length > 1 ? {
        i: common_vendor.f(promoDataList.value, (item, index, i0) => {
          return {
            a: index,
            b: currentIndex.value === index ? 1 : "",
            c: common_vendor.o(($event) => switchToSlide(index), index)
          };
        })
      } : {}) : {}, {
        c: promoDataList.value.length > 0
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4eff421e"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
