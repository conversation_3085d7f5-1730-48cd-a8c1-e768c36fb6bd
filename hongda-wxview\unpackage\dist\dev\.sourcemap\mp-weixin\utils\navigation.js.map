{"version": 3, "file": "navigation.js", "sources": ["utils/navigation.js"], "sourcesContent": ["/**\r\n * 统一导航跳转工具 (推荐版)\r\n * @description 本函数与优化后的后端配套使用，接收包含 linkType 和 linkTarget 的对象。\r\n * @param {object} navItem - 包含 linkType 和 linkTarget 的导航对象\r\n */\r\nexport function navigateTo(navItem) {\r\n    // 检查从后端获取的数据是否有效\r\n    if (!navItem || !navItem.linkType || !navItem.linkTarget) {\r\n        console.warn('无效的导航项，缺少 linkType 或 linkTarget:', navItem);\r\n        return;\r\n    }\r\n\r\n    const { linkType, linkTarget, title } = navItem;\r\n\r\n    // 根据后端指定的类型执行跳转，前端无需猜测\r\n    switch (linkType) {\r\n        case 'INTERNAL_PAGE': // 内部普通页面\r\n            uni.navigateTo({\r\n                url: linkTarget,\r\n                fail: (err) => console.error(`跳转普通页面失败: ${linkTarget}`, err)\r\n            });\r\n            break;\r\n\r\n        case 'TAB_PAGE': // Tab栏页面\r\n            uni.switchTab({\r\n                url: linkTarget,\r\n                fail: (err) => console.error(`跳转 TabBar 失败: ${linkTarget}`, err)\r\n            });\r\n            break;\r\n\r\n        case 'EXTERNAL_LINK': // 外部H5链接\r\n            // 确保项目中存在 /pages/webview/index 页面\r\n            uni.navigateTo({\r\n                url: `/pages/webview/index?url=${encodeURIComponent(linkTarget)}&title=${title || ''}`,\r\n                fail: (err) => console.error(`跳转 Webview 失败: ${linkTarget}`, err)\r\n            });\r\n            break;\r\n\r\n        default:\r\n            console.warn('未知的链接类型:', linkType);\r\n            break;\r\n    }\r\n}"], "names": ["uni"], "mappings": ";;AAKO,SAAS,WAAW,SAAS;AAEhC,MAAI,CAAC,WAAW,CAAC,QAAQ,YAAY,CAAC,QAAQ,YAAY;AACtDA,kBAAA,MAAA,MAAA,QAAA,4BAAa,oCAAoC,OAAO;AACxD;AAAA,EACH;AAED,QAAM,EAAE,UAAU,YAAY,MAAK,IAAK;AAGxC,UAAQ,UAAQ;AAAA,IACZ,KAAK;AACDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK;AAAA,QACL,MAAM,CAAC,QAAQA,cAAA,MAAA,MAAA,SAAA,6BAAc,aAAa,UAAU,IAAI,GAAG;AAAA,MAC3E,CAAa;AACD;AAAA,IAEJ,KAAK;AACDA,oBAAAA,MAAI,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM,CAAC,QAAQA,gEAAc,iBAAiB,UAAU,IAAI,GAAG;AAAA,MAC/E,CAAa;AACD;AAAA,IAEJ,KAAK;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,4BAA4B,mBAAmB,UAAU,CAAC,UAAU,SAAS,EAAE;AAAA,QACpF,MAAM,CAAC,QAAQA,gEAAc,kBAAkB,UAAU,IAAI,GAAG;AAAA,MAChF,CAAa;AACD;AAAA,IAEJ;AACIA,oBAAA,MAAA,MAAA,QAAA,6BAAa,YAAY,QAAQ;AACjC;AAAA,EACP;AACL;;"}