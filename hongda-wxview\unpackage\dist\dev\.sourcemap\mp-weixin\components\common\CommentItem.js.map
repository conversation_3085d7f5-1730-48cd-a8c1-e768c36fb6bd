{"version": 3, "file": "CommentItem.js", "sources": ["components/common/CommentItem.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvY29tbW9uL0NvbW1lbnRJdGVtLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"comment-item\">\r\n    <view class=\"top-comment\">\r\n      <view class=\"comment-avatar\">\r\n        <image :src=\"comment.avatarUrl || defaultAvatarUrl\" class=\"avatar-img\"></image>\r\n      </view>\r\n      <view class=\"comment-content\">\r\n        <view class=\"comment-header\">\r\n          <text class=\"comment-author\">{{ comment.nickname || '匿名用户' }}</text>\r\n          <text class=\"comment-time\">{{ formatDateTime(comment.createTime) }}</text>\r\n        </view>\r\n        <text class=\"comment-text\">{{ comment.content }}</text>\r\n      </view>\r\n    </view>\r\n\r\n\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n\r\nconst props = defineProps({\r\n  comment: {\r\n    type: Object,\r\n    required: true\r\n  }\r\n});\r\n\r\nconst emit = defineEmits([]);\r\nconst assets = ref(uni.getStorageSync('staticAssets') || {});\r\n\r\n// 2. 创建一个计算属性，用于获取默认头像的URL\r\nconst defaultAvatarUrl = computed(() => {\r\n  // 使用我们约定的“暗号” default_avatar\r\n  return assets.value.default_avatar || '';\r\n});\r\n\r\n/**\r\n * 【新增】日期时间格式化函数\r\n * @param {string | Date} time - 需要格式化的时间\r\n * @returns {string} 格式化后的字符串，例如 \"2025-07-21 10:32:21\"\r\n */\r\nconst formatDateTime = (time) => {\r\n  if (!time) return '';\r\n  const date = new Date(time);\r\n\r\n  const Y = date.getFullYear();\r\n  const M = (date.getMonth() + 1).toString().padStart(2, '0');\r\n  const D = date.getDate().toString().padStart(2, '0');\r\n\r\n  const h = date.getHours().toString().padStart(2, '0');\r\n  const m = date.getMinutes().toString().padStart(2, '0');\r\n  const s = date.getSeconds().toString().padStart(2, '0');\r\n\r\n  return `${Y}-${M}-${D} ${h}:${m}:${s}`;\r\n};\r\n\r\n\r\n\r\n\r\n// 跳转登录前记录当前页面，供登录后返回\r\nconst getCurrentPageUrl = () => {\r\n  try {\r\n    const pages = getCurrentPages();\r\n    const current = pages[pages.length - 1];\r\n    const route = '/' + current.route;\r\n    const options = current.options || {};\r\n    const query = Object.keys(options)\r\n      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)\r\n      .join('&');\r\n    return query ? `${route}?${query}` : route;\r\n  } catch (e) {\r\n    return '';\r\n  }\r\n};\r\n\r\n// 统一的登录校验\r\nconst ensureLoggedIn = () => {\r\n  try {\r\n    const token = uni.getStorageSync('token');\r\n    if (!token) {\r\n      const backUrl = getCurrentPageUrl();\r\n      try { if (backUrl) uni.setStorageSync('loginBackPage', backUrl); } catch (e) {}\r\n      uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n      return false;\r\n    }\r\n    return true;\r\n  } catch (e) {\r\n    uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n    return false;\r\n  }\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 样式部分无需修改，但为了完整性一并提供 --- */\r\n.comment-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 顶级评论样式 */\r\n.top-comment {\r\n  display: flex;\r\n  padding: 16px;\r\n  gap: 12px;\r\n}\r\n\r\n.comment-avatar {\r\n  flex-shrink: 0;\r\n\r\n  .avatar-img {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.comment-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.comment-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 8px;\r\n\r\n  .comment-author {\r\n    font-size: 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .comment-time {\r\n    font-size: 13px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.comment-text {\r\n  display: block;\r\n  font-size: 16px;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  margin-bottom: 12px;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n\r\n\r\n\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/common/CommentItem.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni", "computed"], "mappings": ";;;;;;;;;;;;AA8BA,UAAM,SAASA,cAAG,IAACC,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA,CAAE;AAG3D,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AAEtC,aAAO,OAAO,MAAM,kBAAkB;AAAA,IACxC,CAAC;AAOD,UAAM,iBAAiB,CAAC,SAAS;AAC/B,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,OAAO,IAAI,KAAK,IAAI;AAE1B,YAAM,IAAI,KAAK;AACf,YAAM,KAAK,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC1D,YAAM,IAAI,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAEnD,YAAM,IAAI,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACpD,YAAM,IAAI,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACtD,YAAM,IAAI,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAEtD,aAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACtC;;;;;;;;;;;;ACvDA,GAAG,gBAAgB,SAAS;"}