"use strict";
const common_vendor = require("../../common/vendor.js");
const pages_sub_pages_other_api_platform_page = require("./api/platform/page.js");
const utils_date = require("../../utils/date.js");
const utils_mpHtmlStyles = require("../../utils/mpHtmlStyles.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  const _easycom_u_loading_page2 = common_vendor.resolveComponent("u-loading-page");
  (_easycom_u_icon2 + _easycom_mp_html2 + _easycom_u_loading_page2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_mp_html = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
const _easycom_u_loading_page = () => "../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js";
if (!Math) {
  (_easycom_u_icon + _easycom_mp_html + _easycom_u_loading_page)();
}
const _sfc_main = {
  __name: "custom_page",
  setup(__props) {
    const loading = common_vendor.ref(true);
    const pageData = common_vendor.ref(null);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const headerHeight = common_vendor.ref(0);
    const getNavBarInfo = () => {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        navBarHeight.value = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight.value) * 2;
        headerHeight.value = menuButtonInfo.bottom + (menuButtonInfo.top - statusBarHeight.value);
      } catch (e) {
        statusBarHeight.value = 20;
        navBarHeight.value = 44;
        headerHeight.value = 64;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack({ delta: 1 });
    };
    common_vendor.onLoad(async (options) => {
      getNavBarInfo();
      const pageId = options.id;
      if (!pageId) {
        loading.value = false;
        common_vendor.index.__f__("error", "at pages_sub/pages_other/custom_page.vue:90", "页面ID缺失");
        return;
      }
      try {
        const res = await pages_sub_pages_other_api_platform_page.getPageDetailsApi(pageId);
        if (res.code === 200 && res.data) {
          pageData.value = res.data;
        } else {
          pageData.value = null;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/custom_page.vue:102", "获取页面详情失败:", error);
        pageData.value = null;
      } finally {
        loading.value = false;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: pageData.value && !loading.value
      }, pageData.value && !loading.value ? {
        b: statusBarHeight.value + "px",
        c: common_vendor.p({
          name: "arrow-left",
          color: "#000000",
          size: "22"
        }),
        d: common_vendor.o(goBack),
        e: common_vendor.t(pageData.value.title || "详情"),
        f: navBarHeight.value + "px",
        g: headerHeight.value + "px",
        h: common_vendor.t(pageData.value.title),
        i: common_vendor.t(common_vendor.unref(utils_date.formatDate)(pageData.value.createTime, "YYYY-MM-DD")),
        j: common_vendor.p({
          content: pageData.value.content,
          ["tag-style"]: common_vendor.unref(utils_mpHtmlStyles.tagStyle),
          ["lazy-load"]: true
        }),
        k: headerHeight.value + "px"
      } : loading.value ? {
        m: common_vendor.p({
          ["loading-text"]: "正在加载...",
          loading: loading.value
        })
      } : {}, {
        l: loading.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-01161e56"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/custom_page.js.map
