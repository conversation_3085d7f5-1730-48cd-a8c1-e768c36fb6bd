{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <scroll-view class=\"main-scroll-view\" scroll-y @scrolltolower=\"handleScrollToLower\">\r\n      <HeaderComponent />\r\n\r\n      <view>\r\n        <BannerComponent />\r\n      </view>\r\n\r\n      <view>\r\n        <QuickNavigationComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <CountryHighlightComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <EventPromotionComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <NewsListComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <ActivityGridComponent ref=\"activityRef\" @all-loaded-change=\"onAllLoadedChange\"/>\r\n      </view>\r\n\r\n      <view class=\"no-more-divider\" v-if=\"showNoMore\">\r\n        <view class=\"no-more-line\"></view>\r\n        <text class=\"no-more-text\">没有更多了</text>\r\n        <view class=\"no-more-line\"></view>\r\n      </view>\r\n\r\n    </scroll-view>\r\n\r\n    <CustomTabBar :current=\"0\"/>\r\n\r\n    <!-- 动态绑定客服图标 -->\r\n    <view class=\"fab-customer-service\" @click=\"navigateToService\">\r\n      <image class=\"fab-icon\" :src=\"fabIconUrl\" mode=\"aspectFit\"></image>\r\n    </view>\r\n\r\n    <!-- 多个弹窗广告组件 - 只显示当前的一个 -->\r\n    <PopupAdComponent\r\n        v-if=\"showPopupAd && currentAdData\"\r\n        :show=\"showPopupAd\"\r\n        :ad-data=\"currentAdData\"\r\n        @close=\"handlePopupClose\"\r\n    />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onShow, onLoad } from '@dcloudio/uni-app';\r\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\r\nimport HeaderComponent from \"@/components/home/<USER>\";\r\nimport BannerComponent from '@/components/home/<USER>';\r\nimport QuickNavigationComponent from '@/components/home/<USER>';\r\nimport CountryHighlightComponent from '@/components/home/<USER>';\r\nimport NewsListComponent from \"@/components/home/<USER>\";\r\nimport ActivityGridComponent from '@/components/home/<USER>';\r\nimport EventPromotionComponent from '@/components/home/<USER>';\r\nimport PopupAdComponent from '@/components/common/PopupAdComponent.vue';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\n\r\n// --- 新增：与精选活动滚动加载相关的状态 ---\r\nconst activityRef = ref(null)\r\nconst showNoMore = ref(false)\r\n\r\n// --- 状态定义 ---\r\n\r\n// 定义客服图标的URL（仅暗号读取）\r\nconst fabIconUrl = ref('');\r\n\r\n// 弹窗广告相关\r\nconst showPopupAd = ref(false);\r\nconst adList = ref([]); // 存储所有广告数据\r\nconst currentAdIndex = ref(0); // 当前显示的广告索引\r\nconst currentAdData = ref(null); // 当前显示的广告数据\r\nconst AD_POSITION_CODE = 'SPLASH_SCREEN';\r\n\r\n// 会话标记 - 用于判断是否是本次会话首次进入首页\r\nlet hasShownInCurrentSession = false;\r\n\r\n// --- 方法定义 ---\r\n\r\n/**\r\n * 触底回调：当外层 scroll-view 到达底部时，通知精选活动组件加载更多\r\n */\r\nconst handleScrollToLower = () => {\r\n  if (activityRef.value && activityRef.value.loadMore) {\r\n    activityRef.value.loadMore()\r\n  }\r\n}\r\n\r\n/**\r\n * 子组件事件：精选活动是否已全部加载完成\r\n * @param {boolean} finished true 表示所有活动已展示完成\r\n */\r\nconst onAllLoadedChange = (finished) => {\r\n  showNoMore.value = !!finished\r\n}\r\n\r\n/**\r\n * 首次进入或回到首页时检查并显示弹窗广告（会话内仅展示一次）\r\n */\r\nconst checkAndShowPopupAd = async () => {\r\n  try {\r\n    // 如果本次会话已经显示过广告，则不再显示\r\n    if (hasShownInCurrentSession) {\r\n      console.log('本次会话已经显示过弹屏广告，不再显示');\r\n      return;\r\n    }\r\n\r\n    const response = await getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 10 });\r\n\r\n    console.log('弹窗广告API返回结果:', response);\r\n\r\n    if (response && response.data && response.data.length > 0) {\r\n      adList.value = response.data;\r\n      currentAdIndex.value = 0;\r\n\r\n      console.log(`获取到 ${adList.value.length} 个广告，开始显示第一个`);\r\n\r\n      // 显示第一个广告\r\n      showNextAd();\r\n    } else {\r\n      console.log('没有广告数据可显示');\r\n      // 即使没有广告数据，也标记为已显示\r\n      hasShownInCurrentSession = true;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取弹窗广告失败:', error.message || error);\r\n    // 请求失败也标记为已显示\r\n    hasShownInCurrentSession = true;\r\n  }\r\n};\r\n\r\n/**\r\n * 显示下一个广告\r\n */\r\nconst showNextAd = () => {\r\n  if (currentAdIndex.value < adList.value.length) {\r\n    currentAdData.value = adList.value[currentAdIndex.value];\r\n    showPopupAd.value = true;\r\n\r\n    console.log(`显示第 ${currentAdIndex.value + 1} 个广告:`, currentAdData.value.title);\r\n  } else {\r\n    console.log('所有广告已显示完毕');\r\n    showPopupAd.value = false;\r\n    currentAdData.value = null;\r\n  }\r\n};\r\n\r\n/**\r\n * 弹窗关闭处理：继续显示下一条或结束本次会话展示\r\n */\r\nconst handlePopupClose = () => {\r\n  showPopupAd.value = false;\r\n  currentAdIndex.value++;\r\n\r\n  // 检查是否所有广告都显示完了\r\n  if (currentAdIndex.value >= adList.value.length) {\r\n    // 所有广告都显示完毕，标记本次会话为已显示\r\n    hasShownInCurrentSession = true;\r\n    console.log('所有广告显示完毕，已标记本次会话为已显示');\r\n    return;\r\n  }\r\n\r\n  // 延迟一下再显示下一个广告，避免太突兀\r\n  setTimeout(() => {\r\n    showNextAd();\r\n  }, 300);\r\n};\r\n\r\n/**\r\n * 仅测试用途：模拟小程序重启\r\n */\r\nconst simulateAppRestart = () => {\r\n  hasShownInCurrentSession = false;\r\n  console.log('已重置会话状态，模拟小程序重启');\r\n  uni.showToast({\r\n    title: '已模拟重启',\r\n    icon: 'success'\r\n  });\r\n  // 重新检查广告\r\n  checkAndShowPopupAd();\r\n};\r\n\r\n/**\r\n * 跳转到客服/联系我们页面\r\n */\r\nconst navigateToService = () => {\r\n  uni.navigateTo({\r\n    url: '/pages_sub/pages_profile/contact'\r\n  });\r\n};\r\n\r\n// --- 生命周期钩子 ---\r\n\r\n// onLoad 只在页面首次加载时触发，适合判断是否是小程序冷启动\r\nonLoad(() => {\r\n  console.log('首页 onLoad - 页面首次加载');\r\n  // onLoad时不重置状态，因为可能是从其他页面返回\r\n});\r\n\r\nonShow(() => {\r\n  console.log('首页 onShow - 页面显示', {\r\n    hasShownInCurrentSession: hasShownInCurrentSession\r\n  });\r\n\r\n  // 隐藏原生tabbar\r\n  uni.hideTabBar();\r\n\r\n  // 检查并显示弹窗广告\r\n  checkAndShowPopupAd();\r\n\r\n  // 每次页面显示时，从全局缓存读取正确的静态资源数据\r\n  const assets = uni.getStorageSync('staticAssets');\r\n  fabIconUrl.value = assets?.fab_customer_service_icon || '';\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  overflow: hidden;\r\n}\r\n\r\n.main-scroll-view {\r\n  flex: 1;\r\n  height: 0;\r\n  position: relative;\r\n  z-index: 0;\r\n  transform: translateZ(0);\r\n  padding-top: 176rpx;\r\n  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));\r\n  box-sizing: border-box;\r\n\r\n  :deep(.u-loadmore) {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n.fab-customer-service {\r\n  position: fixed;\r\n  right: 20rpx;\r\n  bottom: calc(200rpx + env(safe-area-inset-bottom));\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 50%;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 999;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.fab-customer-service:active {\r\n  opacity: 0.7;\r\n}\r\n\r\n.fab-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n}\r\n\r\n.no-more-divider {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 24rpx 0;\r\n}\r\n\r\n.no-more-text {\r\n  width: 120rpx;\r\n  height: 34rpx;\r\n  line-height: 34rpx;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n  font-weight: normal;\r\n  font-size: 24rpx;\r\n  color: #9B9A9A;\r\n  text-align: center;\r\n  font-style: normal;\r\n  text-transform: none;\r\n}\r\n\r\n.no-more-line {\r\n  width: 40rpx;\r\n  height: 2rpx;\r\n  background: #CBCBCB;\r\n  border-radius: 0rpx;\r\n  margin: 0 12rpx;\r\n  flex-shrink: 0;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "getAdListByPositionApi", "onLoad", "onShow"], "mappings": ";;;;;;AA0DA,MAAM,kBAAkB,MAAW;AACnC,MAAM,kBAAkB,MAAW;AACnC,MAAM,2BAA2B,MAAW;AAC5C,MAAM,4BAA4B,MAAW;AAC7C,MAAM,oBAAoB,MAAW;AACrC,MAAM,wBAAwB,MAAW;AACzC,MAAM,0BAA0B,MAAW;AAC3C,MAAM,mBAAmB,MAAW;AACpC,MAAM,eAAe,MAAW;AAgBhC,MAAM,mBAAmB;;;;AAbzB,UAAM,cAAcA,cAAG,IAAC,IAAI;AAC5B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAK5B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AACrB,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,IAAI;AAI9B,QAAI,2BAA2B;AAO/B,UAAM,sBAAsB,MAAM;AAChC,UAAI,YAAY,SAAS,YAAY,MAAM,UAAU;AACnD,oBAAY,MAAM,SAAU;AAAA,MAC7B;AAAA,IACH;AAMA,UAAM,oBAAoB,CAAC,aAAa;AACtC,iBAAW,QAAQ,CAAC,CAAC;AAAA,IACvB;AAKA,UAAM,sBAAsB,YAAY;AACtC,UAAI;AAEF,YAAI,0BAA0B;AAC5BC,wBAAAA,MAAA,MAAA,OAAA,gCAAY,oBAAoB;AAChC;AAAA,QACD;AAED,cAAM,WAAW,MAAMC,uCAAuB,kBAAkB,EAAE,UAAU,GAAE,CAAE;AAEhFD,sBAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB,QAAQ;AAEpC,YAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AACzD,iBAAO,QAAQ,SAAS;AACxB,yBAAe,QAAQ;AAEvBA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,OAAO,OAAO,MAAM,MAAM,cAAc;AAGpD;QACN,OAAW;AACLA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,WAAW;AAEvB,qCAA2B;AAAA,QAC5B;AAAA,MACF,SAAQ,OAAO;AACdA,2EAAc,aAAa,MAAM,WAAW,KAAK;AAEjD,mCAA2B;AAAA,MAC5B;AAAA,IACH;AAKA,UAAM,aAAa,MAAM;AACvB,UAAI,eAAe,QAAQ,OAAO,MAAM,QAAQ;AAC9C,sBAAc,QAAQ,OAAO,MAAM,eAAe,KAAK;AACvD,oBAAY,QAAQ;AAEpBA,sBAAAA,MAAA,MAAA,OAAA,gCAAY,OAAO,eAAe,QAAQ,CAAC,SAAS,cAAc,MAAM,KAAK;AAAA,MACjF,OAAS;AACLA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,WAAW;AACvB,oBAAY,QAAQ;AACpB,sBAAc,QAAQ;AAAA,MACvB;AAAA,IACH;AAKA,UAAM,mBAAmB,MAAM;AAC7B,kBAAY,QAAQ;AACpB,qBAAe;AAGf,UAAI,eAAe,SAAS,OAAO,MAAM,QAAQ;AAE/C,mCAA2B;AAC3BA,sBAAAA,MAAA,MAAA,OAAA,gCAAY,sBAAsB;AAClC;AAAA,MACD;AAGD,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAmBA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAKAE,kBAAAA,OAAO,MAAM;AACXF,oBAAAA,MAAY,MAAA,OAAA,gCAAA,oBAAoB;AAAA,IAElC,CAAC;AAEDG,kBAAAA,OAAO,MAAM;AACXH,oBAAAA,mDAAY,oBAAoB;AAAA,QAC9B;AAAA,MACJ,CAAG;AAGDA,oBAAG,MAAC,WAAU;AAGd;AAGA,YAAM,SAASA,cAAAA,MAAI,eAAe,cAAc;AAChD,iBAAW,SAAQ,iCAAQ,8BAA6B;AAAA,IAC1D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9ND,GAAG,WAAW,eAAe;"}