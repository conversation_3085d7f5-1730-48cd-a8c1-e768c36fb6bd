<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="文章标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入文章标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文章标签" prop="tagIds">
        <el-select
            v-model="queryParams.tagIds"
            multiple
            filterable
            placeholder="请选择文章标签"
            clearable
            style="width: 240px"
        >
          <el-option
              v-for="item in tagOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
          <el-option
              v-for="dict in hongda_article_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['content:article:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['content:article:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['content:article:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['content:article:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="articleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文章标题" align="center" prop="title" min-width="250" :show-overflow-tooltip="true" />
      <el-table-column label="文章标签" align="center" min-width="180">
        <template #default="scope">
          <div v-if="scope.row.tags && scope.row.tags.length" class="tag-wrapper">
            <el-tag v-for="tag in scope.row.tags" :key="tag.id" style="margin: 2px;">{{ tag.name }}</el-tag>
          </div>
          <span v-else>—</span>
        </template>
      </el-table-column>
      <el-table-column label="内容预览" align="center" prop="content" width="300">
        <template #default="scope">
          <div class="content-preview">
            <div class="preview-text" v-html="generateSmartPreview(scope.row.content)"></div>
            <div class="preview-actions">
              <el-button link type="primary" @click="handleView(scope.row)" size="small">
                查看全文
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="封面图" align="center" prop="coverImageUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.coverImageUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch
              v-model="scope.row.status"
              active-value="1"
              inactive-value="2"
              @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="publishTime" width="180" sortable>
        <template #default="scope">
          <span>{{ scope.row.publishTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="阅读量" align="center" prop="viewCount" width="100" sortable />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['content:article:edit']">修改</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['content:article:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="80%" top="5vh" append-to-body :close-on-click-modal="false">
      <el-form ref="articleRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文章标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入文章标题" maxlength="100" show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文章来源" prop="source">
              <el-input v-model="form.source" placeholder="例如：红大资讯" maxlength="50" show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文章标签" prop="tagIds">
              <el-select v-model="form.tagIds" multiple filterable placeholder="请选择或搜索文章标签" style="width: 100%">
                <el-option v-for="item in tagOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="摘要" prop="summary">
              <el-input v-model="form.summary" type="textarea" :rows="3" placeholder="请输入摘要内容" maxlength="200" show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="封面图" prop="coverImageUrl">
              <image-upload
                  v-model="form.coverImageUrl"
                  :limit="1"
                  recommendation-text="推荐大小为504px * 288px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文章内容" prop="content">
              <el-button type="primary" icon="Edit" @click="editorDrawerOpen = true">
                打开内容编辑器
              </el-button>
              <div style="margin-top: 8px; color: #909399; font-size: 13px;">
                {{ form.content ? '✔️ 已设置内容' : '❌ 未设置内容' }}
                <span v-if="form.content" style="margin-left: 12px;">
                  字数: {{ form.content.replace(/<[^>]*>/g, '').length }}
                </span>
              </div>
            </el-form-item>
          </el-col>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="阅读量" prop="viewCount">
                <el-input-number v-model="form.viewCount" controls-position="right" :min="0" placeholder="请输入阅读量" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示顺序" prop="sortOrder">
                <el-input-number v-model="form.sortOrder" controls-position="right" :min="0"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker v-model="form.publishTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发布时间"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
        v-model="contentDialogVisible"
        width="auto"
        :show-close="false"
        :modal="true"
        :lock-scroll="true"
        align-center
        class="device-preview-dialog"
    >
      <div class="preview-wrapper">
        <div class="preview-container" :style="{ width: previewWidth + 'px', height: previewHeight + 'px' }">
          <MiniProgramPreview
              v-if="contentDialogVisible"
              :article="currentArticleForPreview"
          />
        </div>
        <el-button
            class="preview-close-btn"
            circle
            :icon="Close"
            @click="contentDialogVisible = false"
        />
      </div>
    </el-dialog>

    <el-drawer
        v-model="editorDrawerOpen"
        :with-header="false"
        direction="rtl"
        size="75%"
        :destroy-on-close="true"
        :close-on-click-modal="false"
    >
      <div class="editor-drawer-container">
        <div class="editor-drawer-header">
          <h3>内容编辑器</h3>
          <p>您正在编辑文章: {{ form.title || '新文章' }}</p>
        </div>

        <div class="editor-drawer-body">
          <editor v-model="form.content" height="100%" />
        </div>

        <div class="editor-drawer-footer">
          <div class="footer-left">
            <el-icon><Document /></el-icon>
            <span>{{ form.content ? '内容已保存' : '尚未保存内容' }}</span>
            <el-divider direction="vertical" />
            <span>字数: {{ form.content ? form.content.replace(/<[^>]*>/g, '').length : 0 }}</span>
          </div>
          <div class="footer-right">
            <el-button @click="editorDrawerOpen = false">
              <el-icon><Close /></el-icon>
              取 消
            </el-button>
            <el-button type="primary" @click="editorDrawerOpen = false">
              <el-icon><Check /></el-icon>
              完 成
            </el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup name="Article">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import { listArticle, getArticle, delArticle, addArticle, updateArticle } from "@/api/content/article";
import { listAllTag } from "@/api/content/tag";
import { Document, Close, Check } from '@element-plus/icons-vue';
import he from 'he';

import MiniProgramPreview from '@/components/MiniProgramPreview/index.vue';
import Editor from '@/components/Editor/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';

const { proxy } = getCurrentInstance();
const { hongda_article_status } = proxy.useDict('hongda_article_status');

const articleList = ref([]);
const tagOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const editorDrawerOpen = ref(false);

const data = reactive({
  form: {},
  queryParams: { pageNum: 1, pageSize: 10, title: null, status: null, tagIds: [] },
  rules: {
    title: [{ required: true, message: "文章标题不能为空", trigger: "blur" }],
  }
});
const { queryParams, form, rules } = toRefs(data);

const contentDialogVisible = ref(false);
const currentArticleForPreview = ref(null);

const previewWidth = 390;
const previewHeight = 844;

function handleView(row) {
  if (!row || !row.content) {
    proxy.$modal.msgWarning('该文章暂无内容');
    return;
  }
  const articleData = { ...row };
  const baseUrl = import.meta.env.VITE_APP_BASE_API;

  if (articleData.coverImageUrl && articleData.coverImageUrl.startsWith('/')) {
    articleData.coverImageUrl = baseUrl + articleData.coverImageUrl;
  }
  if (articleData.tags && typeof articleData.tags === 'string') {
    try {
      articleData.tags = JSON.parse(articleData.tags);
    } catch(e) {
      articleData.tags = [];
    }
  } else if (!articleData.tags) {
    articleData.tags = [];
  }

  currentArticleForPreview.value = articleData;
  contentDialogVisible.value = true;
}

function generateSmartPreview(html = '') {
  if (!html) return '<span>（无内容）</span>';
  try {
    const decodedHtml = he.decode(html);
    const plainText = decodedHtml.replace(/<[^>]+>/g, '');
    return plainText.trim().substring(0, 50) + (plainText.length > 50 ? '...' : '');
  } catch (error) {
    return '<span>（预览生成失败）</span>';
  }
}

function getList() {
  loading.value = true;
  listArticle(queryParams.value).then(response => {
    const processedRows = response.rows.map(article => {
      if (article.tags && typeof article.tags === 'string') {
        try {
          article.tags = JSON.parse(article.tags);
        } catch (e) {
          console.error('Failed to parse tags JSON:', article.tags, e);
          article.tags = [];
        }
      } else if (!article.tags) {
        article.tags = [];
      }
      return article;
    });

    articleList.value = processedRows;
    total.value = response.total;
    loading.value = false;
  });
}

async function getTagOptions() {
  const response = await listAllTag();
  tagOptions.value = response.data;
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    id: null, title: null, summary: null, content: '',
    coverImageUrl: null, status: '1',
    publishTime: null, tagIds: [], viewCount: 0
  };
  proxy.resetForm("articleRef");
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.tagIds = [];
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

async function handleAdd() {
  reset();
  await getTagOptions();
  open.value = true;
  title.value = "添加文章";
}

async function handleUpdate(row) {
  reset();
  await getTagOptions();
  const articleId = row.id || ids.value[0];
  const response = await getArticle(articleId);
  form.value = response.data;
  if (!form.value.tagIds) {
    form.value.tagIds = [];
  }
  open.value = true;
  title.value = "修改文章";
}

function submitForm() {
  proxy.$refs["articleRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateArticle(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addArticle(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

function handleDelete(row) {
  const articleIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除文章ID为"' + articleIds + '"的数据项？').then(() => {
    return delArticle(articleIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('content/article/export', { ...queryParams.value }, `article_${new Date().getTime()}.xlsx`);
}

/**
 * 处理文章状态变更
 */
function handleStatusChange(row) {
  let text = row.status === "1" ? "发布" : "下架";
  proxy.$modal.confirm(`确认要将文章【${row.title}】"${text}"吗？`).then(function() {
    return updateArticle({ id: row.id, status: row.status });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    // 如果API调用失败，将开关恢复到原始状态
    row.status = row.status === "1" ? "2" : "1";
  });
}

onMounted(() => {
  getList();
  getTagOptions();
});
</script>

<style scoped>
/* 原有基础样式 */
.tag-wrapper { display: flex; flex-wrap: wrap; justify-content: center; gap: 4px; }
.content-preview { text-align: left; }
.preview-actions { margin-top: 8px; }

/* [优化] 最终版设备预览对话框样式 */
:deep(.device-preview-dialog.el-dialog) {
  background-color: transparent !important;
  box-shadow: none !important;
  --el-dialog-padding-primary: 0;
}
:deep(.device-preview-dialog .el-dialog__header) {
  display: none;
}
:deep(.device-preview-dialog .el-dialog__body) {
  padding: 0;
}

.preview-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-container {
  overflow: hidden;
  border-radius: 40px;
  /* [优化] 使用更细腻的 box-shadow 模拟手机边框 */
  box-shadow:
      inset 0 0 0 2px #555,
      inset 0 0 0 6px #000,
      0 10px 40px rgba(0, 0, 0, 0.3);
}

.preview-close-btn {
  margin-top: 24px; /* 与手机模型保持一定间距 */
  --el-button-size: 50px;
  --el-button-text-color: #ffffff;
  --el-button-bg-color: rgba(0, 0, 0, 0.4);
  --el-button-border-color: rgba(255, 255, 255, 0.3);
  --el-button-hover-text-color: #ffffff;
  --el-button-hover-bg-color: rgba(0, 0, 0, 0.6);
  --el-button-hover-border-color: rgba(255, 255, 255, 0.5);
  font-size: 24px;
}

/* 编辑器抽屉样式 */
.editor-drawer-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--el-bg-color-page);
}

.editor-drawer-header {
  padding: 24px 32px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
}

.editor-drawer-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-drawer-header h3::before {
  content: "📝";
  font-size: 18px;
}

.editor-drawer-header p {
  margin: 12px 0 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 400;
  line-height: 1.5;
}

.editor-drawer-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-drawer-body :deep(.w-e-container) {
  width: 100% !important;
  height: 100% !important;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.editor-drawer-body :deep(.w-e-toolbar) {
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  min-height: auto;
}

.editor-drawer-body :deep(.w-e-toolbar-item) {
  margin: 0 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.editor-drawer-body :deep(.w-e-toolbar-item:hover) {
  background-color: var(--el-fill-color-light);
}

.editor-drawer-body :deep(.w-e-text-container) {
  flex: 1;
  background: #fff;
  border-radius: 0 0 8px 8px;
}

.editor-drawer-body :deep(.w-e-text) {
  padding: 20px 24px !important;
  font-size: 15px;
  line-height: 1.8;
  color: var(--el-text-color-primary);
  min-height: 400px;
}

.editor-drawer-body :deep(.w-e-text:focus) {
  outline: none;
  box-shadow: inset 0 0 0 2px var(--el-color-primary-light-7);
}

.editor-drawer-footer {
  padding: 20px 32px 24px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
}

.editor-drawer-footer .footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.editor-drawer-footer .footer-right {
  display: flex;
  gap: 12px;
}

.editor-drawer-footer .el-button {
  padding: 10px 24px;
  font-weight: 500;
}

.editor-drawer-body::-webkit-scrollbar {
  width: 6px;
}

.editor-drawer-body::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.editor-drawer-body::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

.editor-drawer-body::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

@media (max-width: 1200px) {
  .editor-drawer-header,
  .editor-drawer-body,
  .editor-drawer-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .editor-drawer-body :deep(.w-e-text) {
    padding: 16px 20px !important;
    font-size: 14px;
  }
}

@media (prefers-color-scheme: dark) {
  .editor-drawer-body :deep(.w-e-text-container),
  .editor-drawer-body :deep(.w-e-text) {
    background: var(--el-bg-color);
    color: var(--el-text-color-primary);
  }
}
</style>