{"version": 3, "file": "index.js", "sources": ["pages/webview/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd2Vidmlldy9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view>\r\n    <web-view v-if=\"webviewUrl\" :src=\"webviewUrl\"></web-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n\r\nconst webviewUrl = ref('');\r\n\r\nonLoad((options) => {\r\n  if (options && options.url) {\r\n    // 解码从URL参数中获取的网页链接\r\n    webviewUrl.value = decodeURIComponent(options.url);\r\n\r\n    // [关键修改]：我们不再需要在这里手动设置标题。\r\n    // <web-view> 组件会自动抓取网页的标题并设置它。\r\n    // 这样就避免了标题的二次跳变。\r\n\r\n  } else {\r\n    console.error('No url provided for webview.');\r\n    uni.showToast({\r\n      title: '链接地址无效',\r\n      icon: 'error',\r\n      duration: 2000\r\n    });\r\n    // 如果没有有效的URL，可以将标题设置为错误提示\r\n    uni.setNavigationBarTitle({\r\n      title: '页面加载失败'\r\n    });\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n/* You can add styles here if needed */\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/webview/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni"], "mappings": ";;;;;AAUA,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAEzBC,kBAAM,OAAC,CAAC,YAAY;AAClB,UAAI,WAAW,QAAQ,KAAK;AAE1B,mBAAW,QAAQ,mBAAmB,QAAQ,GAAG;AAAA,MAMrD,OAAS;AACLC,sBAAAA,MAAc,MAAA,SAAA,iCAAA,8BAA8B;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAEDA,sBAAAA,MAAI,sBAAsB;AAAA,UACxB,OAAO;AAAA,QACb,CAAK;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;AChCD,GAAG,WAAW,eAAe;"}