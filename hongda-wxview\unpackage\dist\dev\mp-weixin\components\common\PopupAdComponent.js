"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_navigation = require("../../utils/navigation.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
const _sfc_main = {
  __name: "PopupAdComponent",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    adData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const visible = common_vendor.ref(props.show);
    common_vendor.watch(() => props.show, (newVal) => {
      common_vendor.index.__f__("log", "at components/common/PopupAdComponent.vue:41", "弹窗显示状态变化:", newVal);
      common_vendor.index.__f__("log", "at components/common/PopupAdComponent.vue:42", "广告数据:", props.adData);
      visible.value = newVal;
    }, { immediate: true });
    const adData = common_vendor.computed(() => {
      return props.adData || {};
    });
    const handleAdClick = () => {
      common_vendor.index.__f__("log", "at components/common/PopupAdComponent.vue:53", "广告被点击:", props.adData);
      if (props.adData && typeof utils_navigation.navigateTo === "function") {
        utils_navigation.navigateTo(props.adData);
      }
      closePopup();
    };
    const closePopup = () => {
      common_vendor.index.__f__("log", "at components/common/PopupAdComponent.vue:64", "关闭弹窗");
      visible.value = false;
      emit("close");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: visible.value
      }, visible.value ? {
        b: adData.value.imageUrl,
        c: common_vendor.o(handleAdClick),
        d: common_vendor.p({
          type: "closeempty",
          size: "20",
          color: "#fff"
        }),
        e: common_vendor.o(closePopup),
        f: common_vendor.o(() => {
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ce514586"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/PopupAdComponent.js.map
