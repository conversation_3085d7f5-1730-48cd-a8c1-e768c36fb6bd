{"name": "ruoyi", "version": "3.9.0", "description": "若依管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@form-create/element-ui": "^3.2.15", "@tinymce/tinymce-vue": "^6.2.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.3.0", "@wang-form-create/designer": "^3.2.17", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.9.0", "chart.js": "^4.5.0", "clipboard": "2.0.11", "dompurify": "^3.2.6", "echarts": "^5.6.0", "element-china-area-data": "^6.1.0", "element-plus": "2.9.9", "file-saver": "2.0.5", "fuse.js": "6.6.2", "he": "^1.2.0", "highlight.js": "^11.11.1", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "3.0.2", "prettier": "^3.6.2", "splitpanes": "4.0.4", "tinymce": "^7.9.1", "vue": "3.5.16", "vue-cropper": "1.1.1", "vue-router": "4.5.1", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "sass-embedded": "1.89.1", "unplugin-auto-import": "0.18.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}, "overrides": {"quill": "2.0.2"}}