"use strict";
const MUNICIPALITIES = ["北京", "上海", "天津", "重庆"];
const formatEventLocation = (event) => {
  if (!event)
    return "待定";
  if (event.city && event.city.trim()) {
    const city = event.city.trim();
    const isMunicipality = MUNICIPALITIES.some(
      (municipality) => city.includes(municipality)
    );
    if (isMunicipality) {
      if (event.province && event.province.trim()) {
        const province = event.province.trim();
        const municipalityMatch2 = MUNICIPALITIES.find(
          (municipality) => province.includes(municipality)
        );
        if (municipalityMatch2) {
          return province.endsWith("市") ? province : `${municipalityMatch2}市`;
        }
      }
      const municipalityMatch = MUNICIPALITIES.find(
        (municipality) => city.includes(municipality)
      );
      if (municipalityMatch) {
        return city.endsWith("市") ? city : `${municipalityMatch}市`;
      }
    }
    return city;
  }
  if (event.province && event.province.trim()) {
    const province = event.province.trim();
    const isMunicipality = MUNICIPALITIES.some(
      (municipality) => province.includes(municipality)
    );
    if (isMunicipality) {
      const municipalityMatch = MUNICIPALITIES.find(
        (municipality) => province.includes(municipality)
      );
      return province.endsWith("市") ? province : `${municipalityMatch}市`;
    }
    return province;
  }
  if (event.location && event.location.trim()) {
    const location = event.location.trim();
    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);
    if (match) {
      const province = match[1];
      const city = match[2];
      const isProvinceMunicipality = MUNICIPALITIES.some(
        (municipality) => province.includes(municipality)
      );
      if (isProvinceMunicipality) {
        const municipalityMatch = MUNICIPALITIES.find(
          (municipality) => province.includes(municipality)
        );
        return province.endsWith("市") ? province : `${municipalityMatch}市`;
      }
      if (city && province !== city) {
        return city;
      }
      return province;
    }
    return location;
  }
  return "待定";
};
const formatActivityLocation = (item) => {
  return formatEventLocation(item);
};
exports.formatActivityLocation = formatActivityLocation;
exports.formatEventLocation = formatEventLocation;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/location.js.map
