{"version": 3, "file": "location.js", "sources": ["utils/location.js"], "sourcesContent": ["/**\r\n * 地点相关工具函数\r\n */\r\n\r\n/**\r\n * 直辖市列表\r\n */\r\nconst MUNICIPALITIES = ['北京', '上海', '天津', '重庆'];\r\n\r\n/**\r\n * 统一格式化活动地点显示\r\n * 对于直辖市，优先显示省字段；对于其他城市，优先显示市字段\r\n * @param {Object} event - 活动对象\r\n * @returns {string} 格式化后的地点\r\n */\r\nexport const formatEventLocation = (event) => {\r\n  if (!event) return '待定';\r\n  \r\n  // 优先使用city字段\r\n  if (event.city && event.city.trim()) {\r\n    const city = event.city.trim();\r\n    \r\n    // 检查是否为直辖市\r\n    const isMunicipality = MUNICIPALITIES.some(municipality => \r\n      city.includes(municipality)\r\n    );\r\n    \r\n    if (isMunicipality) {\r\n      // 对于直辖市，如果有province字段且包含直辖市名称，使用province\r\n      if (event.province && event.province.trim()) {\r\n        const province = event.province.trim();\r\n        const municipalityMatch = MUNICIPALITIES.find(municipality => \r\n          province.includes(municipality)\r\n        );\r\n        if (municipalityMatch) {\r\n          // 如果province字段已经包含\"市\"，直接返回；否则添加\"市\"\r\n          return province.endsWith('市') ? province : `${municipalityMatch}市`;\r\n        }\r\n      }\r\n      // 如果province字段不可用，从city字段中提取直辖市名称\r\n      const municipalityMatch = MUNICIPALITIES.find(municipality => \r\n        city.includes(municipality)\r\n      );\r\n      if (municipalityMatch) {\r\n        return city.endsWith('市') ? city : `${municipalityMatch}市`;\r\n      }\r\n    }\r\n    \r\n    // 对于非直辖市，直接返回city字段\r\n    return city;\r\n  }\r\n  \r\n  // 如果没有city字段，尝试使用province字段\r\n  if (event.province && event.province.trim()) {\r\n    const province = event.province.trim();\r\n    \r\n    // 检查province是否为直辖市\r\n    const isMunicipality = MUNICIPALITIES.some(municipality => \r\n      province.includes(municipality)\r\n    );\r\n    \r\n    if (isMunicipality) {\r\n      const municipalityMatch = MUNICIPALITIES.find(municipality => \r\n        province.includes(municipality)\r\n      );\r\n      return province.endsWith('市') ? province : `${municipalityMatch}市`;\r\n    }\r\n    \r\n    return province;\r\n  }\r\n  \r\n  // 如果都没有，尝试从location字段提取\r\n  if (event.location && event.location.trim()) {\r\n    const location = event.location.trim();\r\n    \r\n    // 简单的城市提取逻辑\r\n    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);\r\n    if (match) {\r\n      const province = match[1];\r\n      const city = match[2];\r\n      \r\n      // 检查是否为直辖市\r\n      const isProvinceMunicipality = MUNICIPALITIES.some(municipality => \r\n        province.includes(municipality)\r\n      );\r\n      \r\n      if (isProvinceMunicipality) {\r\n        const municipalityMatch = MUNICIPALITIES.find(municipality => \r\n          province.includes(municipality)\r\n        );\r\n        return province.endsWith('市') ? province : `${municipalityMatch}市`;\r\n      }\r\n      \r\n      if (city && province !== city) {\r\n        return city;\r\n      }\r\n      return province;\r\n    }\r\n    \r\n    return location;\r\n  }\r\n  \r\n  return '待定';\r\n};\r\n\r\n/**\r\n * 活动地点格式化函数（兼容旧的函数名）\r\n * @param {Object} item - 活动对象\r\n * @returns {string} 格式化后的地点\r\n */\r\nexport const formatActivityLocation = (item) => {\r\n  return formatEventLocation(item);\r\n};\r\n"], "names": ["municipalityMatch"], "mappings": ";AAOA,MAAM,iBAAiB,CAAC,MAAM,MAAM,MAAM,IAAI;AAQlC,MAAC,sBAAsB,CAAC,UAAU;AAC5C,MAAI,CAAC;AAAO,WAAO;AAGnB,MAAI,MAAM,QAAQ,MAAM,KAAK,KAAI,GAAI;AACnC,UAAM,OAAO,MAAM,KAAK,KAAI;AAG5B,UAAM,iBAAiB,eAAe;AAAA,MAAK,kBACzC,KAAK,SAAS,YAAY;AAAA,IAChC;AAEI,QAAI,gBAAgB;AAElB,UAAI,MAAM,YAAY,MAAM,SAAS,KAAI,GAAI;AAC3C,cAAM,WAAW,MAAM,SAAS,KAAI;AACpC,cAAMA,qBAAoB,eAAe;AAAA,UAAK,kBAC5C,SAAS,SAAS,YAAY;AAAA,QACxC;AACQ,YAAIA,oBAAmB;AAErB,iBAAO,SAAS,SAAS,GAAG,IAAI,WAAW,GAAGA,kBAAiB;AAAA,QAChE;AAAA,MACF;AAED,YAAM,oBAAoB,eAAe;AAAA,QAAK,kBAC5C,KAAK,SAAS,YAAY;AAAA,MAClC;AACM,UAAI,mBAAmB;AACrB,eAAO,KAAK,SAAS,GAAG,IAAI,OAAO,GAAG,iBAAiB;AAAA,MACxD;AAAA,IACF;AAGD,WAAO;AAAA,EACR;AAGD,MAAI,MAAM,YAAY,MAAM,SAAS,KAAI,GAAI;AAC3C,UAAM,WAAW,MAAM,SAAS,KAAI;AAGpC,UAAM,iBAAiB,eAAe;AAAA,MAAK,kBACzC,SAAS,SAAS,YAAY;AAAA,IACpC;AAEI,QAAI,gBAAgB;AAClB,YAAM,oBAAoB,eAAe;AAAA,QAAK,kBAC5C,SAAS,SAAS,YAAY;AAAA,MACtC;AACM,aAAO,SAAS,SAAS,GAAG,IAAI,WAAW,GAAG,iBAAiB;AAAA,IAChE;AAED,WAAO;AAAA,EACR;AAGD,MAAI,MAAM,YAAY,MAAM,SAAS,KAAI,GAAI;AAC3C,UAAM,WAAW,MAAM,SAAS,KAAI;AAGpC,UAAM,QAAQ,SAAS,MAAM,+CAA+C;AAC5E,QAAI,OAAO;AACT,YAAM,WAAW,MAAM,CAAC;AACxB,YAAM,OAAO,MAAM,CAAC;AAGpB,YAAM,yBAAyB,eAAe;AAAA,QAAK,kBACjD,SAAS,SAAS,YAAY;AAAA,MACtC;AAEM,UAAI,wBAAwB;AAC1B,cAAM,oBAAoB,eAAe;AAAA,UAAK,kBAC5C,SAAS,SAAS,YAAY;AAAA,QACxC;AACQ,eAAO,SAAS,SAAS,GAAG,IAAI,WAAW,GAAG,iBAAiB;AAAA,MAChE;AAED,UAAI,QAAQ,aAAa,MAAM;AAC7B,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACR;AAED,SAAO;AACT;AAOY,MAAC,yBAAyB,CAAC,SAAS;AAC9C,SAAO,oBAAoB,IAAI;AACjC;;;"}