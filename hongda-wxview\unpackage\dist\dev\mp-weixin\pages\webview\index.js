"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const webviewUrl = common_vendor.ref("");
    common_vendor.onLoad((options) => {
      if (options && options.url) {
        webviewUrl.value = decodeURIComponent(options.url);
      } else {
        common_vendor.index.__f__("error", "at pages/webview/index.vue:23", "No url provided for webview.");
        common_vendor.index.showToast({
          title: "链接地址无效",
          icon: "error",
          duration: 2e3
        });
        common_vendor.index.setNavigationBarTitle({
          title: "页面加载失败"
        });
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: webviewUrl.value
      }, webviewUrl.value ? {
        b: webviewUrl.value
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a96d96f3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/webview/index.js.map
