<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>hongda</artifactId>
        <groupId>com.hongda</groupId>
        <version>3.9.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hongda-modules</artifactId>

    <packaging>jar</packaging>

    <description>
        hongda-modules: 统一业务功能模块 (存放内容、活动、用户等所有业务代码)
    </description>

    <dependencies>

        <dependency>
            <groupId>com.hongda</groupId>
            <artifactId>hongda-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hongda</groupId>
            <artifactId>hongda-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations-jakarta</artifactId>
            <version>2.2.28</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>

    </dependencies>

</project>