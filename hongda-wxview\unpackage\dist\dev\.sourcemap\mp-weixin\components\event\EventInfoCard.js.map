{"version": 3, "file": "EventInfoCard.js", "sources": ["components/event/EventInfoCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRJbmZvQ2FyZC52dWU"], "sourcesContent": ["<template>\n  <view class=\"info-card\">\n    <view class=\"card-header\">\n      <view :class=\"['status-tag-detail', getRegistrationStatusClass(calculatedRegistrationStatus)]\">\n        <image class=\"status-bg-image\" :src=\"detailBgUrl\" mode=\"aspectFit\"></image>\n        <text class=\"status-text\">{{ formatRegistrationStatus(calculatedRegistrationStatus) }}</text>\n      </view>\n      <view class=\"event-title-section\">\n        <text class=\"event-title\">{{ localEvent.title || '' }}</text>\n      </view>\n    </view>\n\n    <view class=\"info-row\">\n      <image class=\"info-icon\" :src=\"detailTimeIconUrl\" mode=\"aspectFit\"></image>\n      <text class=\"info-text\">{{ formatEventTime }}</text>\n    </view>\n    <view class=\"info-row\">\n      <image class=\"info-icon\" :src=\"detailLocationIconUrl\" mode=\"aspectFit\"></image>\n      <text class=\"info-text\">{{ formatEventLocation(localEvent) }}</text>\n    </view>\n    <view class=\"info-row\">\n      <image class=\"info-icon\" :src=\"detailUserIconUrl\" mode=\"aspectFit\"></image>\n      <rich-text :nodes=\"remainingSpotsNodes\" class=\"info-text\"></rich-text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { computed, ref, onMounted } from 'vue'\nimport { formatEventStatus, getStatusClass } from '@/utils/tools.js'\nimport { formatDate } from '@/utils/date.js'\nimport { formatEventLocation } from '@/utils/location.js'\n\nconst props = defineProps({\n  eventDetail: { type: Object, required: true }\n})\n\n// 静态资源 URL（不再使用本地兜底）\nconst detailBgUrl = ref('')\nconst detailTimeIconUrl = ref('')\nconst detailLocationIconUrl = ref('')\nconst detailUserIconUrl = ref('')\n\n// 组件挂载时读取静态资源配置\nonMounted(() => {\n  const assets = uni.getStorageSync('staticAssets')\n  \n  detailBgUrl.value = assets?.detail_bg || ''\n  detailTimeIconUrl.value = assets?.detail_icon_time || ''\n  detailLocationIconUrl.value = assets?.detail_icon_location || ''\n  detailUserIconUrl.value = assets?.detail_icon_user || ''\n})\n\nconst localEvent = computed(() => props.eventDetail || {})\n\n// formatEventLocation 函数已从 @/utils/location.js 导入\n\n/**\n * 计算报名状态，与后端RegistrationStatus.calculateStatus逻辑保持一致\n * 0: 即将开始 (当前时间 < 报名开始时间)\n * 1: 报名中 (报名开始时间 <= 当前时间 <= 报名结束时间)  \n * 2: 报名截止 (当前时间 > 报名结束时间)\n */\nconst calculatedRegistrationStatus = computed(() => {\n  const registrationStartTime = localEvent.value?.registrationStartTime\n  const registrationEndTime = localEvent.value?.registrationEndTime\n  \n  try {\n    const now = new Date()\n    \n    // 未开始：报名开始时间不为空 && 当前时间 < 报名开始时间\n    if (registrationStartTime && now < new Date(registrationStartTime)) {\n      return 0 // 即将开始\n    }\n    // 报名中：(报名开始时间为空 || 当前时间 >= 报名开始时间) && (报名结束时间为空 || 当前时间 <= 报名结束时间)\n    else if ((!registrationStartTime || now >= new Date(registrationStartTime)) && \n             (!registrationEndTime || now <= new Date(registrationEndTime))) {\n      return 1 // 报名中\n    }\n    // 已结束：报名结束时间不为空 && 当前时间 > 报名结束时间\n    else if (registrationEndTime && now > new Date(registrationEndTime)) {\n      return 2 // 报名截止\n    }\n    \n    // 默认返回报名中（如果时间配置不明确）\n    return 1\n  } catch (error) {\n    console.warn('报名状态计算失败:', error)\n    return 1 // 默认显示报名中\n  }\n})\n\n/**\n * 格式化报名状态文本，与EventCard保持一致\n */\nconst formatRegistrationStatus = (status) => {\n  switch (status) {\n    case 0: return '即将开始'\n    case 1: return '报名中'\n    case 2: return '报名截止'\n    default: return '未知'\n  }\n}\n\n/**\n * 获取报名状态样式类，与EventCard保持一致\n */\nconst getRegistrationStatusClass = (status) => {\n  switch (status) {\n    case 0: return 'not-started'  // 未开始\n    case 1: return 'open'         // 报名中\n    case 2: return 'ended'        // 已截止\n    default: return 'unknown'\n  }\n}\n\nconst formatEventTime = computed(() => {\n  if (!localEvent.value?.startTime || !localEvent.value?.endTime) {\n    return '时间待定'\n  }\n  try {\n    const startTime = formatDate(localEvent.value.startTime, 'YYYY-MM-DD HH:mm')\n    const endTime = formatDate(localEvent.value.endTime, 'YYYY-MM-DD HH:mm')\n    return `${startTime} 至 ${endTime}`\n  } catch (error) {\n    console.warn('时间格式化失败:', error)\n    return '时间格式错误'\n  }\n})\n\nconst remainingSpotsNodes = computed(() => {\n  if (!localEvent.value) {\n    return [{ type: 'text', text: '加载中...' }]\n  }\n\n  const max = Number(localEvent.value.maxParticipants) || 0\n  if (max === 0) {\n    return [{ type: 'text', text: '剩余名额: 不限人数' }]\n  }\n\n  const registered = Number(localEvent.value.registeredCount) || 0\n  const remaining = Math.max(0, max - registered)\n\n  return [\n    {\n      type: 'node',\n      name: 'span',\n      children: [\n        { type: 'text', text: '剩余名额: ' },\n        {\n          type: 'node',\n          name: 'span',\n          attrs: { style: 'color: #023F98;' },\n          children: [{ type: 'text', text: String(remaining) }]\n        },\n        { type: 'text', text: `/${max}` }\n      ]\n    }\n  ]\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.info-card {\n  background: #FFFFFF;\n  margin: 0 30rpx;\n  margin-top: -60rpx;\n  border-radius: 16rpx 16rpx 16rpx 16rpx;\n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);\n  padding: 30rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.info-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n}\n\n.status-tag-detail {\n  width: 90rpx;\n  height: 40rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n  font-size: 22rpx;\n  border-radius: 12rpx;\n  margin-right: 16rpx;\n  margin-top: 15rpx;\n  flex-shrink: 0;\n  \n  // 与EventCard保持一致的状态样式\n  &.ended {\n    background: #9B9A9A;\n    color: #FFFFFF;\n  }\n  &.ended .status-bg-image {\n    display: none;\n  }\n  \n  &.not-started {\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\n    color: #23232A;\n  }\n  &.not-started .status-bg-image {\n    display: none;\n  }\n  \n  &.open {\n    background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\n    color: #23232A;\n  }\n  &.open .status-bg-image {\n    display: none;\n  }\n}\n\n.event-title-section {\n  flex: 1;\n  min-width: 0;\n  margin-left: 16rpx;\n}\n\n.event-title {\n  white-space: normal;\n  word-break: break-word;\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n  font-weight: normal;\n  font-size: 32rpx;\n  color: #23232A;\n  line-height: 1.5;\n}\n\n.info-row {\n  display: flex;\n  align-items: center;\n  margin-top: 24rpx;\n}\n\n.info-text {\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\n  font-size: 26rpx;\n  color: #606266;\n  margin-left: 16rpx;\n}\n\n.status-bg-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.status-text {\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n  font-weight: normal;\n  font-size: 22rpx;\n  color: #023F98;\n  position: relative;\n  z-index: 2;\n  \n  // 根据父级状态调整文字颜色\n  .status-tag-detail.ended & {\n    color: #FFFFFF;\n  }\n  \n  .status-tag-detail.not-started & {\n    color: #23232A;\n  }\n  \n  .status-tag-detail.open & {\n    color: #23232A;\n  }\n}\n</style>\n\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventInfoCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni", "computed", "formatDate"], "mappings": ";;;;;;;;;;AAiCA,UAAM,QAAQ;AAKd,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,oBAAoBA,cAAG,IAAC,EAAE;AAChC,UAAM,wBAAwBA,cAAG,IAAC,EAAE;AACpC,UAAM,oBAAoBA,cAAG,IAAC,EAAE;AAGhCC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,kBAAY,SAAQ,iCAAQ,cAAa;AACzC,wBAAkB,SAAQ,iCAAQ,qBAAoB;AACtD,4BAAsB,SAAQ,iCAAQ,yBAAwB;AAC9D,wBAAkB,SAAQ,iCAAQ,qBAAoB;AAAA,IACxD,CAAC;AAED,UAAM,aAAaC,cAAAA,SAAS,MAAM,MAAM,eAAe,CAAA,CAAE;AAUzD,UAAM,+BAA+BA,cAAQ,SAAC,MAAM;;AAClD,YAAM,yBAAwB,gBAAW,UAAX,mBAAkB;AAChD,YAAM,uBAAsB,gBAAW,UAAX,mBAAkB;AAE9C,UAAI;AACF,cAAM,MAAM,oBAAI,KAAM;AAGtB,YAAI,yBAAyB,MAAM,IAAI,KAAK,qBAAqB,GAAG;AAClE,iBAAO;AAAA,QACR,YAES,CAAC,yBAAyB,OAAO,IAAI,KAAK,qBAAqB,OAC/D,CAAC,uBAAuB,OAAO,IAAI,KAAK,mBAAmB,IAAI;AACvE,iBAAO;AAAA,QACR,WAEQ,uBAAuB,MAAM,IAAI,KAAK,mBAAmB,GAAG;AACnE,iBAAO;AAAA,QACR;AAGD,eAAO;AAAA,MACR,SAAQ,OAAO;AACdD,sBAAAA,MAAA,MAAA,QAAA,4CAAa,aAAa,KAAK;AAC/B,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAKD,UAAM,2BAA2B,CAAC,WAAW;AAC3C,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAKA,UAAM,6BAA6B,CAAC,WAAW;AAC7C,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;;AACrC,UAAI,GAAC,gBAAW,UAAX,mBAAkB,cAAa,GAAC,gBAAW,UAAX,mBAAkB,UAAS;AAC9D,eAAO;AAAA,MACR;AACD,UAAI;AACF,cAAM,YAAYC,WAAAA,WAAW,WAAW,MAAM,WAAW,kBAAkB;AAC3E,cAAM,UAAUA,WAAAA,WAAW,WAAW,MAAM,SAAS,kBAAkB;AACvE,eAAO,GAAG,SAAS,MAAM,OAAO;AAAA,MACjC,SAAQ,OAAO;AACdF,sBAAAA,MAAA,MAAA,QAAA,6CAAa,YAAY,KAAK;AAC9B,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,sBAAsBC,cAAQ,SAAC,MAAM;AACzC,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,SAAQ,CAAE;AAAA,MACzC;AAED,YAAM,MAAM,OAAO,WAAW,MAAM,eAAe,KAAK;AACxD,UAAI,QAAQ,GAAG;AACb,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,aAAY,CAAE;AAAA,MAC7C;AAED,YAAM,aAAa,OAAO,WAAW,MAAM,eAAe,KAAK;AAC/D,YAAM,YAAY,KAAK,IAAI,GAAG,MAAM,UAAU;AAE9C,aAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,YACR,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO,EAAE,OAAO,kBAAmB;AAAA,cACnC,UAAU,CAAC,EAAE,MAAM,QAAQ,MAAM,OAAO,SAAS,GAAG;AAAA,YACrD;AAAA,YACD,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,GAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;AC9JD,GAAG,gBAAgB,SAAS;"}