"use strict";
const common_vendor = require("../../common/vendor.js");
const api_platform_ad = require("../../api/platform/ad.js");
if (!Math) {
  (HeaderComponent + BannerComponent + QuickNavigationComponent + CountryHighlightComponent + EventPromotionComponent + NewsListComponent + ActivityGridComponent + CustomTabBar + PopupAdComponent)();
}
const HeaderComponent = () => "../../components/home/<USER>";
const BannerComponent = () => "../../components/home/<USER>";
const QuickNavigationComponent = () => "../../components/home/<USER>";
const CountryHighlightComponent = () => "../../components/home/<USER>";
const NewsListComponent = () => "../../components/home/<USER>";
const ActivityGridComponent = () => "../../components/home/<USER>";
const EventPromotionComponent = () => "../../components/home/<USER>";
const PopupAdComponent = () => "../../components/common/PopupAdComponent.js";
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const AD_POSITION_CODE = "SPLASH_SCREEN";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const activityRef = common_vendor.ref(null);
    const showNoMore = common_vendor.ref(false);
    const fabIconUrl = common_vendor.ref("");
    const showPopupAd = common_vendor.ref(false);
    const adList = common_vendor.ref([]);
    const currentAdIndex = common_vendor.ref(0);
    const currentAdData = common_vendor.ref(null);
    let hasShownInCurrentSession = false;
    const handleScrollToLower = () => {
      if (activityRef.value && activityRef.value.loadMore) {
        activityRef.value.loadMore();
      }
    };
    const onAllLoadedChange = (finished) => {
      showNoMore.value = !!finished;
    };
    const checkAndShowPopupAd = async () => {
      try {
        if (hasShownInCurrentSession) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:114", "本次会话已经显示过弹屏广告，不再显示");
          return;
        }
        const response = await api_platform_ad.getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 10 });
        common_vendor.index.__f__("log", "at pages/index/index.vue:120", "弹窗广告API返回结果:", response);
        if (response && response.data && response.data.length > 0) {
          adList.value = response.data;
          currentAdIndex.value = 0;
          common_vendor.index.__f__("log", "at pages/index/index.vue:126", `获取到 ${adList.value.length} 个广告，开始显示第一个`);
          showNextAd();
        } else {
          common_vendor.index.__f__("log", "at pages/index/index.vue:131", "没有广告数据可显示");
          hasShownInCurrentSession = true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:136", "获取弹窗广告失败:", error.message || error);
        hasShownInCurrentSession = true;
      }
    };
    const showNextAd = () => {
      if (currentAdIndex.value < adList.value.length) {
        currentAdData.value = adList.value[currentAdIndex.value];
        showPopupAd.value = true;
        common_vendor.index.__f__("log", "at pages/index/index.vue:150", `显示第 ${currentAdIndex.value + 1} 个广告:`, currentAdData.value.title);
      } else {
        common_vendor.index.__f__("log", "at pages/index/index.vue:152", "所有广告已显示完毕");
        showPopupAd.value = false;
        currentAdData.value = null;
      }
    };
    const handlePopupClose = () => {
      showPopupAd.value = false;
      currentAdIndex.value++;
      if (currentAdIndex.value >= adList.value.length) {
        hasShownInCurrentSession = true;
        common_vendor.index.__f__("log", "at pages/index/index.vue:169", "所有广告显示完毕，已标记本次会话为已显示");
        return;
      }
      setTimeout(() => {
        showNextAd();
      }, 300);
    };
    const navigateToService = () => {
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_profile/contact"
      });
    };
    common_vendor.onLoad(() => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:206", "首页 onLoad - 页面首次加载");
    });
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:211", "首页 onShow - 页面显示", {
        hasShownInCurrentSession
      });
      common_vendor.index.hideTabBar();
      checkAndShowPopupAd();
      const assets = common_vendor.index.getStorageSync("staticAssets");
      fabIconUrl.value = (assets == null ? void 0 : assets.fab_customer_service_icon) || "";
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.sr(activityRef, "1cf27b2a-6", {
          "k": "activityRef"
        }),
        b: common_vendor.o(onAllLoadedChange),
        c: showNoMore.value
      }, showNoMore.value ? {} : {}, {
        d: common_vendor.o(handleScrollToLower),
        e: common_vendor.p({
          current: 0
        }),
        f: fabIconUrl.value,
        g: common_vendor.o(navigateToService),
        h: showPopupAd.value && currentAdData.value
      }, showPopupAd.value && currentAdData.value ? {
        i: common_vendor.o(handlePopupClose),
        j: common_vendor.p({
          show: showPopupAd.value,
          ["ad-data"]: currentAdData.value
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
