<template>
  <view class="event-list-page">
    <!-- 1. 头部背景和导航栏区域 -->
    <view class="header-wrapper">
      <!-- 背景图片 - 完全覆盖状态栏 -->
      <image class="header-bg" :src="eventBgUrl" mode="aspectFill"></image>

      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <up-icon name="arrow-left" size="24" color="#FFFFFF"></up-icon>
        </view>
        <view class="navbar-title">
          <text class="title-text">热门活动列表</text>
        </view>
        <view class="navbar-right">
          <!-- 移除更多按钮 -->
        </view>
      </view>

      <!-- 第一行 (Subsection + Search Bar) -->
      <view class="top-controls">
        <up-subsection :list="['列表', '日历']" :current="currentTab" @change="tabChange" mode="subsection"
                       activeColor="#f56c6c"></up-subsection>

        <view class="search-wrapper">
          <CustomSearchBox
              v-model="searchKeyword"
              placeholder="搜索活动"
              @search="onSearch"
              @input="debouncedSearch"
          ></CustomSearchBox>
        </view>
      </view>
    </view>

    <!-- 2.1 列表视图的筛选栏 (移到scroll-view外部) -->
    <view v-if="currentTab === 0" class="filter-bar sticky-filter-bar">
      <!-- 筛选按钮组 -->
      <view class="filter-main-buttons">
        <!-- 综合排序按钮 -->
        <view class="filter-button" @click="toggleSortPanel">
          <text class="filter-text">{{ getCurrentSortTitleList }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showSortPanel }"></up-icon>
        </view>
        <!-- 热门地区按钮 -->
        <view class="filter-button" @click="toggleLocationPanel">
          <text class="filter-text">{{ getCurrentLocationTitleList }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showLocationPanel }"></up-icon>
        </view>
        <!-- 全部时间按钮 -->
        <view class="filter-button" @click="toggleTimePanel">
          <text class="filter-text">{{ getCurrentTimeTitleList }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showTimePanel }"></up-icon>
        </view>
        <!-- 全部状态按钮 -->
        <view class="filter-button" @click="toggleStatusPanel">
          <text class="filter-text">{{ getCurrentStatusTitleList }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showStatusPanel }"></up-icon>
        </view>
      </view>

      <!-- 综合排序面板 -->
      <view v-if="showSortPanel" class="filter-panel">
        <text class="section-title">排序</text>
        <view class="option-grid">
          <view
              v-for="option in options1"
              :key="option.value"
              :class="['option-item', { 'active': tempFiltersList.sortBy === option.value }]"
              @click="selectSortOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetSortFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeSortFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 地区筛选面板 -->
      <view v-if="showLocationPanel" class="filter-panel">
        <!-- 全部地区 -->
        <view class="option-grid" style="margin-bottom: 20rpx;">
          <view
              :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === allRegionOption.value) }]"
              @click="selectLocationOption(allRegionOption.value)"
          >
            <text class="option-text">{{ allRegionOption.label }}</text>
          </view>
        </view>
        
        <text class="section-title">热门地区</text>
        <view class="option-grid">
          <view
              v-for="option in hotCities"
              :key="option.value"
              :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === option.value) }]"
              @click="selectLocationOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        
        <!-- 其他地区 -->
        <view v-if="otherCities.length > 0" style="margin-top: 20rpx;">
          <text class="section-title">其他地区</text>
          <view class="option-grid">
            <view
                v-for="(city, index) in otherCities"
                :key="100 + index"
                :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === (100 + index)) }]"
                @click="selectLocationOption(100 + index)"
            >
              <text class="option-text">{{ city }}</text>
            </view>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetLocationFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeLocationFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 时间筛选面板 -->
      <view v-if="showTimePanel" class="filter-panel">
        <text class="section-title">时间</text>
        <view class="option-grid">
          <view
              v-for="option in options3"
              :key="option.value"
              :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.timeRange : tempFiltersList.timeRange) === option.value) }]"
              @click="selectTimeOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetTimeFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeTimeFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 状态筛选面板 -->
      <view v-if="showStatusPanel" class="filter-panel">
        <text class="section-title">全部状态</text>
        <view class="option-grid">
          <view
              v-for="option in options4"
              :key="option.value"
              :class="['option-item', { 'active': tempFiltersList.status === option.value }]"
              @click="selectStatusOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetStatusFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeStatusFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 2.2 活动列表视图 -->
    <scroll-view v-if="currentTab === 0" scroll-y class="event-list-scroll list-scroll-with-filter"
                 @scrolltolower="onLoadMore"
                 refresher-enabled :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">

      <!-- 空状态 -->
      <view v-if="!isLoading && eventList.length === 0" class="empty-state">
        <up-empty mode="data" text="暂无活动数据" textColor="#909399" iconSize="120"></up-empty>

        <!-- 重试按钮 -->
        <view v-if="showRetry" class="retry-container">
          <up-button type="primary" size="normal" @click="fetchEventList">
            重新加载
          </up-button>
        </view>
      </view>

      <!-- 活动卡片列表 -->
      <EventCard
        v-for="event in eventList"
        :key="event.id"
        :event="event"
        @click="goToDetail(event)"
      />

      <!-- 加载更多组件 -->
      <view class="loadmore-wrapper">
        <up-loadmore :status="loadMoreStatus" :loading-text="'正在加载...'" :loadmore-text="'上拉加载更多'"
                     :nomore-text="'没有更多了'"/>
      </view>
    </scroll-view>

    <!-- 2.3 日历视图的筛选栏 -->
    <view v-if="currentTab === 1" class="filter-bar calendar-filter-bar sticky-filter-bar">
      <!-- 筛选按钮组 - 与列表视图样式保持一致 -->
      <view class="filter-main-buttons">
        <!-- 热门地区按钮 -->
        <view class="filter-button" @click="toggleLocationPanel">
          <text class="filter-text">{{ getCurrentLocationTitleCalendar }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showLocationPanel }"></up-icon>
        </view>
        <!-- 全部时间按钮 -->
        <view class="filter-button" @click="toggleTimePanel">
          <text class="filter-text">{{ getCurrentTimeTitleCalendar }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showTimePanel }"></up-icon>
        </view>
        <!-- 空占位按钮 -->
        <view class="filter-button filter-placeholder">
          <text class="filter-text"></text>
        </view>
        <!-- 空占位按钮 -->
        <view class="filter-button filter-placeholder">
          <text class="filter-text"></text>
        </view>
      </view>

      <!-- 地区筛选面板 -->
      <view v-if="showLocationPanel" class="filter-panel">
        <!-- 全部地区 -->
        <view class="option-grid" style="margin-bottom: 20rpx;">
          <view
              :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === allRegionOption.value) }]"
              @click="selectLocationOption(allRegionOption.value)"
          >
            <text class="option-text">{{ allRegionOption.label }}</text>
          </view>
        </view>
        
        <text class="section-title">热门地区</text>
        <view class="option-grid">
          <view
              v-for="option in hotCities"
              :key="option.value"
              :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === option.value) }]"
              @click="selectLocationOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        
        <!-- 其他地区 -->
        <view v-if="otherCities.length > 0" style="margin-top: 20rpx;">
          <text class="section-title">其他地区</text>
          <view class="option-grid">
            <view
                v-for="(city, index) in otherCities"
                :key="100 + index"
                :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.location : tempFiltersList.location) === (100 + index)) }]"
                @click="selectLocationOption(100 + index)"
            >
              <text class="option-text">{{ city }}</text>
            </view>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetLocationFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeLocationFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 时间筛选面板 -->
      <view v-if="showTimePanel" class="filter-panel">
        <text class="section-title">时间</text>
        <view class="option-grid">
          <view
              v-for="option in options3"
              :key="option.value"
              :class="['option-item', { 'active': ((currentTab === 1 ? tempFiltersCalendar.timeRange : tempFiltersList.timeRange) === option.value) }]"
              @click="selectTimeOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetTimeFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeTimeFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 2.4 日历视图 -->
    <scroll-view v-if="currentTab === 1" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter"
                 @scrolltolower="onLoadMore" refresher-enabled :refresher-triggered="isRefreshing"
                 @refresherrefresh="onRefresh">

      <!-- 空状态 -->
      <view v-if="!isLoading && limitedCalendarEvents.length === 0" class="empty-state">
        <up-empty mode="data" text="暂无活动数据" textColor="#909399" iconSize="120"></up-empty>

        <!-- 重试按钮 -->
        <view v-if="showRetry" class="retry-container">
          <up-button type="primary" size="normal" @click="fetchEventList">
            重新加载
          </up-button>
        </view>
      </view>

      <EventCalendarTimeline
        v-else
        :groups="limitedCalendarEvents"
        :has-more="hasMoreCalendarEvents"
        :notch-left="calendarNotchLeft"
        @click-item="goToDetail"
        @view-more="switchToListView"
      />

      <!-- 底部安全间距，防止被自定义TabBar遮挡 -->
      <view class="calendar-bottom-safe" />


    </scroll-view>

    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="2"/>
  </view>
</template>

<script setup>
import {
  ref,
  computed
} from 'vue';
import {
  onLoad,
  onReachBottom,
  onPullDownRefresh,
  onUnload,
  onShow
} from '@dcloudio/uni-app';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';
import CustomSearchBox from '@/components/home/<USER>';
import EventCard from '@/components/event/EventCard.vue';
import EventCalendarTimeline from '@/components/event/EventCalendarTimeline.vue';
import {
  getEventListApi,
  getCalendarEventsApi,
  getEventCitiesApi
} from '@/api/data/event.js';
import {
  formatEventStatus,
  getStatusClass,
  calculateRemainingSpots,
  debounce
} from '@/utils/tools.js';
import { formatEventDate, parseDate } from '@/utils/date.js';
import {
  PAGE_CONFIG
} from '@/utils/config.js';
import {getFullImageUrl} from '@/utils/image.js';

// ==================== 响应式数据定义 ====================
const currentTab = ref(0); // 默认显示列表视图
const searchKeyword = ref('');
const eventList = ref([]);
const isLoading = ref(false);
const isRefreshing = ref(false);
const showRetry = ref(false);

// 静态资源 URL（不再使用本地兜底）
const eventBgUrl = ref('');

// 分页相关
const pagination = ref({
  pageNum: 1,
  pageSize: PAGE_CONFIG.DEFAULT_PAGE_SIZE,
  total: 0,
  hasMore: true
});

// 已应用的筛选条件（列表与日历视图分离）
const appliedFiltersList = ref({
  sortBy: 1,
  location: 1,
  timeRange: 1,
  status: 1
});
const appliedFiltersCalendar = ref({
  location: 1,
  timeRange: 1
});

// 临时筛选条件（用于UI显示，未应用到数据；列表与日历视图分离）
const tempFiltersList = ref({
  sortBy: 1,
  location: 1,
  timeRange: 1,
  status: 1
});
const tempFiltersCalendar = ref({
  location: 1,
  timeRange: 1
});

// 控制各个筛选面板显示
const showSortPanel = ref(false);
const showLocationPanel = ref(false);
const showTimePanel = ref(false);
const showStatusPanel = ref(false);
  // 日历视图 corner-notch 位置：默认指向"热门地区"60rpx
  const calendarNotchLeft = ref('60rpx');

// 日历视图显示控制 

// 下拉选项配置
const options1 = ref([{
  label: '综合排序',
  value: 1
},
  {
    label: '最新发布',
    value: 2

  },
  {
    label: '最近开始',
    value: 3
  }
]);

// 地区选项配置
const allRegionOption = ref({ label: '全部地区', value: 1 });

// 热门城市配置（不包含全部地区）
const hotCities = ref([
  { label: '北京', value: 2 },
  { label: '上海', value: 3 },
  { label: '广州', value: 4 },
  { label: '深圳', value: 5 }
]);

// 其他城市数据（从API获取）
const otherCities = ref([]);

// 合并的地区选项
const options2 = computed(() => [
  allRegionOption.value,
  ...hotCities.value,
  ...otherCities.value.map((city, index) => ({
    label: city,
    value: 100 + index // 避免与热门城市ID冲突
  }))
]);

const options3 = ref([{
  label: '全部时间',
  value: 1
},
  {
    label: '1周内',
    value: 2
  },
  {
    label: '1月内',
    value: 3
  },
  {
    label: '1年内',
    value: 4
  }
]);

const options4 = ref([{
  label: '全部状态',
  value: 1
},
  {
    label: '即将开始',
    value: 2
  },
  {
    label: '报名中',
    value: 3
  },
  {
    label: '报名截止',
    value: 4
  }
]);

// ==================== 计算属性 ====================
const loadMoreStatus = computed(() => {
  if (isLoading.value) return 'loading';
  if (!pagination.value.hasMore) return 'nomore';
  return 'more';
});

/**
 * 按日期分组活动数据 - 用于日历视图
 */
const groupedEvents = computed(() => {
  if (!eventList.value || eventList.value.length === 0) {
    return [];
  }
  const groups = new Map();
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  eventList.value.forEach(event => {
    const eventDate = parseDate(event.startTime);
    // 💥 关键修改：处理不同年份的日期显示
    const year = eventDate.getFullYear();
    const currentYear = new Date().getFullYear();
    const month = String(eventDate.getMonth() + 1).padStart(2, '0');
    const day = String(eventDate.getDate()).padStart(2, '0');

    let displayDate;
    if (year !== currentYear) {
      displayDate = `${year}年${month}月${day}日`;
    } else {
      displayDate = `${month}.${day}`;
    }

    // 避免使用 toISOString 造成的时区偏移，改用本地日期计算 key
    const monthLocal = String(eventDate.getMonth() + 1).padStart(2, '0');
    const dayLocal = String(eventDate.getDate()).padStart(2, '0');
    const dateKey = `${eventDate.getFullYear()}-${monthLocal}-${dayLocal}`;
    if (!groups.has(dateKey)) {
      groups.set(dateKey, {
        date: dateKey,
        formattedDate: displayDate, // 使用新的日期格式
        dayOfWeek: weekdays[eventDate.getDay()],
        events: []
      });
    }
    groups.get(dateKey).events.push(event);
  });
  return Array.from(groups.values());
});

/**
 * 限制日历视图显示的活动数量 - 最多显示10个活动
 */
const limitedCalendarEvents = computed(() => {
  // 日历视图始终限制为最多10个活动
  let totalEvents = 0;
  const limitedGroups = [];

  for (const group of groupedEvents.value) {
    if (totalEvents >= 10) break;

    const remainingSlots = 10 - totalEvents;
    const eventsToShow = group.events.slice(0, remainingSlots);

    limitedGroups.push({
      ...group,
      events: eventsToShow
    });

    totalEvents += eventsToShow.length;
  }

  return limitedGroups;
});

/**
 * 检查是否有更多活动需要显示
 */
const hasMoreCalendarEvents = computed(() => {
  const totalEvents = groupedEvents.value.reduce((sum, group) => sum + group.events.length, 0);
  return totalEvents > 10;
});

// ==================== 核心方法 ====================

/**
 * 获取活动城市列表
 */
const fetchEventCities = async () => {
  try {
    const response = await getEventCitiesApi();
    
    // 尝试不同的数据提取方式
    let cityList = null;
    if (Array.isArray(response)) {
      cityList = response;
    } else if (response && Array.isArray(response.data)) {
      cityList = response.data;
    } else if (response && response.code === 200 && Array.isArray(response.data)) {
      cityList = response.data;
    }
    
    console.log('提取的城市列表:', cityList);
    
    if (cityList && Array.isArray(cityList)) {
      // 过滤掉热门城市中已有的城市
      const hotCityNames = hotCities.value.map(city => city.label);
      const filteredCities = cityList.filter(city => 
        city && city.trim() && !hotCityNames.includes(city.trim())
      );
      
      otherCities.value = filteredCities;
    } else {
      uni.showToast({
        title: '城市数据格式错误',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('获取城市列表失败:', error);
    uni.showToast({
      title: '获取城市列表失败',
      icon: 'none',
      duration: 2000
    });
  }
};

/**
 * 格式化活动地点 - 使用统一的地点格式化函数
 * 对于直辖市，会正确显示为"上海市"而不是"上海"
 */
const formatEventLocation = (event) => {
  // 直辖市列表
  const MUNICIPALITIES = ['北京', '上海', '天津', '重庆'];
  
  if (!event) return '待定';
  
  // 优先使用city字段
  if (event.city && event.city.trim()) {
    const city = event.city.trim();
    
    // 检查是否为直辖市
    const isMunicipality = MUNICIPALITIES.some(municipality => 
      city.includes(municipality)
    );
    
    if (isMunicipality) {
      // 对于直辖市，如果有province字段且包含直辖市名称，使用province
      if (event.province && event.province.trim()) {
        const province = event.province.trim();
        const municipalityMatch = MUNICIPALITIES.find(municipality => 
          province.includes(municipality)
        );
        if (municipalityMatch) {
          // 如果province字段已经包含"市"，直接返回；否则添加"市"
          return province.endsWith('市') ? province : `${municipalityMatch}市`;
        }
      }
      // 如果province字段不可用，从city字段中提取直辖市名称
      const municipalityMatch = MUNICIPALITIES.find(municipality => 
        city.includes(municipality)
      );
      if (municipalityMatch) {
        return city.endsWith('市') ? city : `${municipalityMatch}市`;
      }
    }
    
    // 对于非直辖市，直接返回city字段
    return city;
  }
  
  // 如果没有city字段，尝试使用province字段
  if (event.province && event.province.trim()) {
    const province = event.province.trim();
    
    // 检查province是否为直辖市
    const isMunicipality = MUNICIPALITIES.some(municipality => 
      province.includes(municipality)
    );
    
    if (isMunicipality) {
      const municipalityMatch = MUNICIPALITIES.find(municipality => 
        province.includes(municipality)
      );
      return province.endsWith('市') ? province : `${municipalityMatch}市`;
    }
    
    return province;
  }
  
  return '待定';
};
/**
  * 构建查询参数（按当前视图分别读取筛选条件）
 */
const buildQueryParams = (isLoadMore = false) => {
  const params = {
    pageNum: isLoadMore ? pagination.value.pageNum : 1,
    pageSize: pagination.value.pageSize
  };

  // 搜索关键词
  if (searchKeyword.value.trim()) {
    params.title = searchKeyword.value.trim();
  }

  // 依据当前视图选择对应的筛选集合
  const filters = currentTab.value === 1 ? appliedFiltersCalendar.value : appliedFiltersList.value;

  // 地区筛选
  if (filters.location > 1) {
    // 热门城市映射
    const hotLocationMap = {
      2: '北京',
      3: '上海',
      4: '广州',
      5: '深圳'
    };
    
    if (hotLocationMap[filters.location]) {
      // 热门城市
      params.location = hotLocationMap[filters.location];
    } else if (filters.location >= 100) {
      // 其他城市（从otherCities数组获取）
      const otherIndex = filters.location - 100;
      if (otherIndex < otherCities.value.length) {
        params.location = otherCities.value[otherIndex];
      }
    }
  }

  // 报名状态筛选（仅列表视图）
  if (currentTab.value === 0 && filters.status > 1) {
    const registrationStatusMap = {
      2: 0, // 即将开始 -> registrationStatus: 0
      3: 1, // 报名中 -> registrationStatus: 1
      4: 2  // 报名截止 -> registrationStatus: 2
    };

    if (registrationStatusMap.hasOwnProperty(filters.status)) {
      params.registrationStatus = registrationStatusMap[filters.status];
    } else {
      console.warn('未知的报名状态筛选值:', filters.status);
    }
  }

  // 排序设置
  if (currentTab.value === 1) {
    // 如果是日历视图，强制按开始时间升序排序
    params.orderBy = 'startTime';
    params.isAsc = 'asc';
  } else {
    // 列表视图排序逻辑 - 支持综合排序（使用已应用的筛选条件）
    switch (filters.sortBy) {
      case 1: // 综合排序
        params.orderBy = 'comprehensive';
        break;
      case 2: // 按时间（最近开始）
        params.orderBy = 'startTime';
        params.isAsc = 'asc';
        console.log('按时间排序: 最近开始优先');
        break;
      case 3: // 按最新发布
        params.orderBy = 'createTime';
        params.isAsc = 'desc';
        console.log('按最新发布排序: 最新创建的活动优先');
        break;
      default: // 默认按创建时间（最新发布）
        params.orderBy = 'createTime';
        params.isAsc = 'desc';
        console.log('默认排序: 最新发布');
    }
  }

  // 时间范围筛选 - 新的时间筛选逻辑（使用已应用的筛选条件）
  if (filters.timeRange > 1) {
    const timeRange = buildTimeRangeParams(filters.timeRange);
    if (timeRange) {
      Object.assign(params, timeRange);
    }
  }

  return params;
};

/**
 * 构建时间范围筛选参数 - 独立的时间筛选逻辑
 * @param {number} timeRangeValue 时间范围选项值
 * @returns {object|null} 时间筛选参数对象
 */
const buildTimeRangeParams = (timeRangeValue) => {
  const now = new Date();
  let startTime = null;
  let endTime = null;

  switch (timeRangeValue) {
    case 2: // 1周内
      startTime = now;
      endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      console.log('时间筛选: 1周内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());
      break;
    case 3: // 1月内
      startTime = now;
      endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
      console.log('时间筛选: 1月内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());
      break;
    case 4: // 1年内
      startTime = now;
      endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
      console.log('时间筛选: 1年内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());
      break;
    default:
      console.log('时间筛选: 全部时间');
      return null;
  }

  return {
    timeRangeStart: startTime.toISOString(),
    timeRangeEnd: endTime.toISOString()
  };
};

/**
 * 获取活动列表
 */
const fetchEventList = async (isLoadMore = false) => {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    const params = buildQueryParams(isLoadMore);
    console.log('请求参数:', params);

    // 根据当前视图选择不同的API
    let response;
    if (currentTab.value === 1) {
      // 日历视图使用专门的API
      response = await getCalendarEventsApi(params);
    } else {
      // 列表视图使用通用API
      response = await getEventListApi(params);
    }
    const {
      rows = [], total = 0
    } = response;

    if (isLoadMore) {
      eventList.value.push(...rows);
    } else {
      eventList.value = rows;
      pagination.value.pageNum = 1;
    }

    pagination.value.total = total;
    pagination.value.hasMore = eventList.value.length < total;

    // 成功加载后隐藏重试按钮
    showRetry.value = false;

    console.log(`获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);

  } catch (error) {
    console.error('获取活动列表失败:', error);

    // 根据错误类型提供不同的提示
    let errorMessage = '获取活动列表失败';
    if (error.message && error.message.includes('timeout')) {
      errorMessage = '网络请求超时，请重试';
    } else if (error.message && error.message.includes('Network')) {
      errorMessage = '网络连接失败，请检查网络';
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });

    // 如果是首次加载失败，显示重试按钮
    if (!isLoadMore && eventList.value.length === 0) {
      showRetry.value = true;
    }
  } finally {
    isLoading.value = false;
    isRefreshing.value = false;
  }
};

/**
 * 搜索防抖处理
 */
const debouncedSearch = debounce(() => {
  fetchEventList();
}, 500);

// ==================== 计算属性 ====================
/**
 * 获取当前排序方式的显示标题（列表视图）
 */
const getCurrentSortTitleList = computed(() => {
  const currentOption = options1.value.find(option => option.value === appliedFiltersList.value.sortBy);
  const title = currentOption ? currentOption.label : '综合排序';
  return title;
});

/**
 * 获取当前状态筛选的显示标题（列表视图）
 */
const getCurrentStatusTitleList = computed(() => {
  const currentOption = options4.value.find(option => option.value === appliedFiltersList.value.status);
  const title = currentOption ? currentOption.label : '全部状态';
  return title;
});

/**
 * 获取当前地区筛选的显示标题（列表视图）
 */
const getCurrentLocationTitleList = computed(() => {
  const currentOption = options2.value.find(option => option.value === appliedFiltersList.value.location);
  return currentOption ? currentOption.label : '热门地区';
});

/**
 * 获取当前时间筛选的显示标题（列表视图）
 */
const getCurrentTimeTitleList = computed(() => {
  const currentOption = options3.value.find(option => option.value === appliedFiltersList.value.timeRange);
  return currentOption ? currentOption.label : '全部时间';
});

/**
 * 获取当前地区筛选的显示标题（日历视图）
 */
const getCurrentLocationTitleCalendar = computed(() => {
  const currentOption = options2.value.find(option => option.value === appliedFiltersCalendar.value.location);
  return currentOption ? currentOption.label : '热门地区';
});

/**
 * 获取当前时间筛选的显示标题（日历视图）
 */
const getCurrentTimeTitleCalendar = computed(() => {
  const currentOption = options3.value.find(option => option.value === appliedFiltersCalendar.value.timeRange);
  return currentOption ? currentOption.label : '全部时间';
});

// ==================== 事件处理方法 ====================
/**
 * 返回按钮处理
 */
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      // 如果没有上一页，则跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

/**
 * 标签页切换
 */
const tabChange = (index) => {
  currentTab.value = index; // 直接使用 index，因为 u-subsection 的 @change 事件直接返回索引号
  // 关键：切换后立即重置数据，以保证用户看到即时加载效果
  eventList.value = [];
  pagination.value.pageNum = 1;
  pagination.value.hasMore = true;
  // 重新调用数据获取函数，此时 buildQueryParams 会根据新的 currentTab 值应用正确的排序
  fetchEventList();
};

/**
 * 搜索处理
 */
const onSearch = (value) => {
  searchKeyword.value = value;
  debouncedSearch();
};

/**
 * 筛选条件变更
 */
const onFilterChange = () => {
  console.log('筛选条件变更，重置数据并重新加载');
  // 重置分页状态
  eventList.value = [];
  pagination.value.pageNum = 1;
  pagination.value.hasMore = true;
  // 重新获取数据
  fetchEventList();
};

/**
 * 切换排序面板显示状态
 */
const toggleSortPanel = () => {
  showSortPanel.value = !showSortPanel.value;
  // 关闭其他面板
  showLocationPanel.value = false;
  showTimePanel.value = false;
  showStatusPanel.value = false;
};

/**
 * 切换地区面板显示状态
 */
const toggleLocationPanel = () => {
  showLocationPanel.value = !showLocationPanel.value;
  // 关闭其他面板
  showSortPanel.value = false;
  showTimePanel.value = false;
  showStatusPanel.value = false;
  // 指向"热门地区"
  calendarNotchLeft.value = '60rpx';
};

/**
 * 切换时间面板显示状态
 */
const toggleTimePanel = () => {
  showTimePanel.value = !showTimePanel.value;
  // 关闭其他面板
  showSortPanel.value = false;
  showLocationPanel.value = false;
  showStatusPanel.value = false;
  // 指向“全部时间”
  calendarNotchLeft.value = '240rpx';
};

/**
 * 切换状态面板显示状态
 */
const toggleStatusPanel = () => {
  showStatusPanel.value = !showStatusPanel.value;
  // 关闭其他面板
  showSortPanel.value = false;
  showLocationPanel.value = false;
  showTimePanel.value = false;
  // 状态面板打开时保持上一次位置，不做指向变动
};

/**
 * 选择排序选项（仅更新临时筛选条件，不立即应用）
 */
const selectSortOption = (value) => {
  // 排序只作用于列表视图
  tempFiltersList.value.sortBy = value;
  console.log('临时选择排序:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 选择地区选项（仅更新临时筛选条件，不立即应用）
 */
const selectLocationOption = (value) => {
  if (currentTab.value === 1) {
    tempFiltersCalendar.value.location = value;
  } else {
    tempFiltersList.value.location = value;
  }
  console.log('临时选择地区:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 选择时间选项（仅更新临时筛选条件，不立即应用）
 */
const selectTimeOption = (value) => {
  if (currentTab.value === 1) {
    tempFiltersCalendar.value.timeRange = value;
  } else {
    tempFiltersList.value.timeRange = value;
  }
  console.log('临时选择时间:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 选择状态选项（仅更新临时筛选条件，不立即应用）
 */
const selectStatusOption = (value) => {
  // 状态只作用于列表视图
  tempFiltersList.value.status = value;
  console.log('临时选择状态:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 重置排序筛选（重置为页面初始状态并立即应用）
 */
const resetSortFilter = () => {
  // 重置临时筛选条件为初始值
  tempFiltersList.value.sortBy = 1;
  // 立即应用重置后的筛选条件（仅列表）
  appliedFiltersList.value.sortBy = 1;
  showSortPanel.value = false;
  console.log('重置排序筛选为初始状态');
  onFilterChange();
};

/**
 * 完成排序筛选（应用临时筛选条件并关闭面板）
 */
const completeSortFilter = () => {
  // 将临时筛选条件应用到已应用筛选条件（仅列表）
  appliedFiltersList.value.sortBy = tempFiltersList.value.sortBy;
  showSortPanel.value = false;
  console.log('应用排序筛选:', tempFiltersList.value.sortBy);
  onFilterChange();
};

/**
 * 重置地区筛选（重置为页面初始状态并立即应用）
 */
const resetLocationFilter = () => {
  if (currentTab.value === 1) {
    tempFiltersCalendar.value.location = 1;
    appliedFiltersCalendar.value.location = 1;
  } else {
    tempFiltersList.value.location = 1;
    appliedFiltersList.value.location = 1;
  }
  showLocationPanel.value = false;
  console.log('重置地区筛选为初始状态');
  onFilterChange();
};

/**
 * 完成地区筛选（应用临时筛选条件并关闭面板）
 */
const completeLocationFilter = () => {
  if (currentTab.value === 1) {
    appliedFiltersCalendar.value.location = tempFiltersCalendar.value.location;
  } else {
    appliedFiltersList.value.location = tempFiltersList.value.location;
  }
  showLocationPanel.value = false;
  console.log('应用地区筛选:', currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location);
  onFilterChange();
  // 选择完成后指向"热门地区"
  calendarNotchLeft.value = '60rpx';
};

/**
 * 重置时间筛选（重置为页面初始状态并立即应用）
 */
const resetTimeFilter = () => {
  if (currentTab.value === 1) {
    tempFiltersCalendar.value.timeRange = 1;
    appliedFiltersCalendar.value.timeRange = 1;
  } else {
    tempFiltersList.value.timeRange = 1;
    appliedFiltersList.value.timeRange = 1;
  }
  showTimePanel.value = false;
  console.log('重置时间筛选为初始状态');
  onFilterChange();
};

/**
 * 完成时间筛选（应用临时筛选条件并关闭面板）
 */
const completeTimeFilter = () => {
  if (currentTab.value === 1) {
    appliedFiltersCalendar.value.timeRange = tempFiltersCalendar.value.timeRange;
  } else {
    appliedFiltersList.value.timeRange = tempFiltersList.value.timeRange;
  }
  showTimePanel.value = false;
  console.log('应用时间筛选:', currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange);
  onFilterChange();
  // 选择完成后仍保持指向"全部时间"
  calendarNotchLeft.value = '240rpx';
};

/**
 * 重置状态筛选（重置为页面初始状态并立即应用）
 */
const resetStatusFilter = () => {
  // 状态仅列表视图生效
  tempFiltersList.value.status = 1;
  appliedFiltersList.value.status = 1;
  showStatusPanel.value = false;
  console.log('重置状态筛选为初始状态');
  onFilterChange();
};

/**
 * 完成状态筛选（应用临时筛选条件并关闭面板）
 */
const completeStatusFilter = () => {
  // 状态仅列表视图生效
  appliedFiltersList.value.status = tempFiltersList.value.status;
  showStatusPanel.value = false;
  console.log('应用状态筛选:', tempFiltersList.value.status);
  onFilterChange();
};

/**
 * 跳转到活动详情
 */
const goToDetail = (event) => {
  uni.navigateTo({
    url: `/pages_sub/pages_event/detail?id=${event.id}`
  });
};

/**
 * 切换到列表视图 - 查看更多活动按钮点击处理
 */
const switchToListView = () => {
  currentTab.value = 0; // 切换到列表视图
  fetchEventList();
};



/**
 * 下拉刷新
 */
const onRefresh = () => {
  isRefreshing.value = true;
  pagination.value.pageNum = 1;
  fetchEventList();
};

/**
 * 上拉加载更多
 */
const onLoadMore = () => {
  if (!pagination.value.hasMore || isLoading.value) return;

  pagination.value.pageNum++;
  fetchEventList(true);
};

// ==================== 生命周期 ====================
onLoad(() => {
  // 读取静态资源配置
  const assets = uni.getStorageSync('staticAssets');
  eventBgUrl.value = assets?.eventbg || '';

  // 获取城市列表和活动列表
  fetchEventCities();
  fetchEventList();

  // 【数据实时更新方案】监听全局数据变化事件
  uni.$on('dataChanged', () => {
    console.log('活动列表页收到数据变化事件，刷新列表...');

    // 重新获取活动列表数据
    fetchEventList();

    uni.showToast({
      title: '列表已更新',
      icon: 'success',
      duration: 1500
    });
  });
});

// 页面显示时隐藏原生 tabBar
onShow(() => {
  uni.hideTabBar();
});

// 页面卸载时移除事件监听
onUnload(() => {
  uni.$off('dataChanged');
});

onReachBottom(() => {
  onLoadMore();
});

onPullDownRefresh(() => {
  onRefresh();
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1000);
});
</script>

<style lang="scss" scoped>
.event-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}


.header-wrapper {
  /* 修改为固定定位，滚动时不移动 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100; /* 确保头部在最上层 */
  overflow: hidden;
  min-height: calc(452rpx + var(--status-bar-height));
  /* 新增：底部圆角，使其与下方白色区域过渡更自然 */
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  padding-bottom: 20rpx;
}

/* 背景图片样式 - 完全覆盖状态栏 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* 确保图片按比例缩放，避免变形 */
  object-fit: cover;
  /* 图片居中显示 */
  object-position: center;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: absolute;
  top: 94rpx;
  left: 0;
  right: 0;
  width: 750rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  z-index: 2;
  border-radius: 0rpx;
}

.navbar-left,
.navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-text {
  width: 190rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 44rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

/* 确保内容在背景图片之上 */
.top-controls,
.filter-bar {
  position: relative;
  z-index: 2;
}

.top-controls {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  gap: 32rpx;
  position: absolute;
  top: 182rpx;
  left: 16rpx;
  right: 24rpx;
  /* 确保子元素完全水平对齐 */
  height: 60rpx; /* 调整为适应新的搜索框高度 */
}

.search-wrapper {
  width: 446rpx;
  height: 60rpx; /* 调整为与新的搜索框高度一致 */
  flex-shrink: 0; /* 防止被压缩 */
  display: flex;
  align-items: center;
  /* 确保与up-subsection组件基线对齐 */
  vertical-align: middle;
}


.filter-bar {
  position: relative;
  background-color: transparent;
  padding: 0;
  margin-bottom: 10rpx;
}

/* 主筛选按钮容器 */
.filter-main-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
}

/* 单个筛选按钮 */
.filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 8rpx 20rpx;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}

.filter-text {
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 26rpx;
  color: #66666E;
  line-height: 44rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* 箭头旋转动画 */
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* 筛选面板*/
.filter-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  width: 750rpx;
  // height: 446rpx;
  max-height: 60vh;
  overflow-y: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 32rpx 24rpx;
  margin: 0 auto;
  max-height: 1046rpx;
  overflow-y: auto;
}

/* 💥 新增：通用的筛选栏吸顶样式 */
.sticky-filter-bar {
  position: fixed;
  /* 头部蓝色区域的高度，可根据实际情况微调 */
  top: calc(260rpx + var(--status-bar-height));
  left: 0;
  right: 0;
  z-index: 102; /* 必须比 scroll-view (101) 更高 */
  background: #FFFFFF;
  box-shadow: none;
  padding-top: 0rpx;
  padding-bottom: 0rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  overflow: visible;
}

/* 💥 新增：为带筛选栏的scroll-view添加顶部内边距 */
.list-scroll-with-filter {
  /* 为列表视图的筛选栏预留空间 */
  padding-top: 56rpx !important; /* 贴近筛选栏，去除多余空白 */
}

.calendar-scroll-with-filter {
  /* 为日历视图的筛选栏预留空间 */
  padding-top: 56rpx !important; /* 贴近筛选栏，去除多余空白 */
}

/* 日历视图筛选栏样式 - 与列表视图保持一致 */
.calendar-filter-bar {
  /* 占位按钮样式 */
  .filter-placeholder {
    pointer-events: none; /* 禁用点击 */
    opacity: 0; /* 隐藏但保持布局空间 */
  }
}


/* 筛选分组 */
.filter-section {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 分组标题 */
.section-title {
  width: 104rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 26rpx;
  color: #23232A;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 24rpx; /* Keeping this for spacing */
  display: block; /* Keeping this for layout */
}

/* 选项网格 */
.option-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx; /* 进一步减少间距 */
  justify-content: flex-start; /* 改为左对齐，避免挤压 */
}

/* 选项项 - 根据设计规格更新 */
.option-item {
  width: 162rpx; /* 计算后的合适宽度 */
  height: 60rpx;
  background: #F2F4FA; /* 未选中状态的背景色 */
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    background: rgba(42, 97, 241, 0.2); /* 选中状态：蓝色背景 + 20%透明度 */
    border-color: transparent;

    .option-text {
      color: #023F98; /* 选中状态的文字颜色 */
      font-weight: normal;
    }
  }

  &:hover {
    opacity: 0.8;
  }
}

.option-text {
  width: 112rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #66666E; /* 未选中状态的文字颜色 */
  line-height: 44rpx;
  text-align: center; /* 修改为居中对齐 */
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 筛选按钮容器 */
.filter-buttons {
  display: flex;
  justify-content: flex-start;
  padding: 32rpx 0 24rpx;
  border-top: 2rpx solid #EEEEEE; /* 添加上边框线 */
  margin-top: 32rpx; /* 增加上边距 */
}

/* 筛选按钮基础样式 */
.filter-btn {
  width: 340rpx; /* 更新宽度为340rpx */
  height: 76rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 24rpx;
  border: none;

  &:last-child {
    margin-right: 0;
  }
}

/* 重置按钮样式 */
.reset-btn {
  background: rgba(42, 97, 241, 0.1); /* 蓝色背景 + 10%透明度 */
}

/* 完成按钮样式 */
.complete-btn {
  background: #023F98; /* 深蓝色背景 */

  &:hover {
    background: #1E4FD9;
  }

  &:active {
    background: #1A43C1;
  }
}

/* 按钮文字基础样式 */
.btn-text {
  width: 56rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* 重置按钮文字样式 */
.reset-btn .btn-text {
  color: #23232A; /* 深灰色文字 */
}

/* 完成按钮文字样式 */
.complete-btn .btn-text {
  color: #FFFFFF; /* 白色文字 */
}

/* 活动列表滚动区域 - 白色内容容器 (根据蓝湖数据精确设置) */
.event-list-scroll {
  /* 将白色背景、圆角、外边距等样式应用到这里 */
  background-color: #ffffff;
  border-top-left-radius: 32rpx; /* 与筛选栏圆角一致，过渡更自然 */
  border-top-right-radius: 32rpx;
  margin: 0;
  margin-top: calc(452rpx + var(--status-bar-height) - 212rpx);
  position: relative;
  z-index: 101;
  padding-top: 8rpx; /* 减少顶部留白 */

  flex: 1;
  box-sizing: border-box;
  padding-bottom: calc(220rpx + env(safe-area-inset-bottom));
  height: calc(100vh - 452rpx - var(--status-bar-height) + 212rpx);
}

/* 日历视图滚动区域  */
.calendar-scroll {
  /* 将白色背景、圆角、外边距等样式应用到这里 */
  background-color: #ffffff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  margin: 0;
  margin-top: -158rpx;
  position: relative;
  padding-top: 24rpx;

  /* 左右内边距，让卡片和筛选栏不要贴边 */
  padding-left: 30rpx;
  padding-right: 30rpx;

  flex: 1;
  box-sizing: border-box;
  padding-bottom: 180rpx; /* 为底部tabBar留空 */
}

.event-card {
  width: 100%;
  height: 272rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  // border: none;
  // border-bottom: 1rpx solid #F5F5F5;
  border: 2rpx solid #EEEEEE;
  margin-bottom: 0rpx;
  padding: 24rpx;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;

  &:last-child {
    border-bottom: none;
  }
}

.card-left {
  position: relative;
  width: 336rpx;
  height: 192rpx;
  flex-shrink: 0;
}

.event-image {
  width: 100%;
  height: 100%;
  display: block;
}

.status-tag {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  width: 90rpx;
  height: 40rpx;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  /* 内部文字样式 */
  color: #23232A;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 22rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.ended {
    background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);
  }
}

.card-right {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.event-title {
  width: 346rpx;
  height: 80rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.event-info-row {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 24rpx !important;
  margin-bottom: 18rpx !important;
  flex-wrap: nowrap !important;
}

.time-location-item {
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
  flex-shrink: 0 !important;
}

.event-info-icon {
  width: 32rpx !important;
  height: 32rpx !important;
  flex-shrink: 0 !important;
}

.info-text {
  width: 176rpx !important;
  height: 32rpx !important;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;
  font-weight: normal !important;
  font-size: 22rpx !important;
  color: #9B9A9A !important;
  text-align: left !important;
  font-style: normal !important;
  text-transform: none !important;
  line-height: 32rpx !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.remaining-spots {
  width: 154rpx;
  height: 40rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FB8620;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;

  /**
   * 剩余名额文字样式
   */
  .spots-count {
    width: 100%;
    height: 36rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
    font-weight: normal;
    font-size: 20rpx;
    color: #FB8620;
    text-align: center;
    font-style: normal;
    text-transform: none;
    line-height: 36rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 空状态样式
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

// 重试按钮容器
.retry-container {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

// 加载状态优化
.event-list-scroll {
  :deep(.up-loadmore) {
    padding: 30rpx 0;
  }
}

/* 为加载更多组件的包裹容器提供上下内边距 */
.loadmore-wrapper {
  padding-top: 40rpx;
  padding-bottom: 20rpx;
}

// 搜索框优化 - 自定义高度设置
.search-wrapper {
  :deep(.up-search) {
    width: 446rpx;
    height: 40rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    .u-search__content {
      width: 100%;
      height: 100%;
      background: #FFFFFF;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);
      display: flex;
      align-items: center;
    }

    .u-search__content__icon {
      color: #c0c4cc;
    }

    .u-search__content__input {
      color: #303133;
    }
  }
}

// 响应式优化
@media screen and (max-width: 750rpx) {
  .top-controls {
    flex-direction: column;
    gap: 16rpx;

    .search-wrapper {
      width: 446rpx;
      align-self: center;
    }
  }
}

/* up-subsection */
:deep(.u-subsection) {
  width: 224rpx !important;
  height: 60rpx !important;
  background: #FFFFFF !important;
  border-radius: 30rpx 30rpx 30rpx 30rpx !important;
  border: none !important;
  box-shadow: none !important;
  overflow: hidden !important;
  position: relative !important; 
  display: flex !important;
  align-items: center !important;
  vertical-align: middle !important;
}

:deep(.u-subsection__item:not(:first-child)) {
  border-left: none !important;
}

:deep(.u-subsection__item) {
  border: none !important;
  padding: 0 !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 50% !important;
  position: relative !important;
  z-index: 2 !important;
}

:deep(.u-subsection__item__text) {
  width: 64rpx !important;
  height: 44rpx !important;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif !important;
  font-weight: normal !important;
  font-size: 32rpx !important;
  color: #23232A !important;
  text-align: center !important;
  font-style: normal !important;
  text-transform: none !important;
  white-space: nowrap !important;
  line-height: 44rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.u-subsection__item--active .u-subsection__item__text) {
  color: #23232A !important; 
  font-weight: normal !important; 
}

/* 移动色块样式 - 确保对称居中且不被覆盖 */
:deep(.u-subsection__bar) {
  width: 96rpx !important;
  height: 60rpx !important;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%) !important;
  border-radius: 30rpx !important;
  transition: all 0.3s ease !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  transform-origin: center center !important;
}

//  日历视图基础样式 
.calendar-view {
  background-color: #FFFFFF;
  padding: 0;
  padding-bottom: 0;
  box-sizing: border-box;
}

/* 搜索框样式  */
.search-wrapper {
  /* 强制覆盖父容器的样式限制 */
  height: 60rpx !important; 
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}


.search-wrapper :deep(.u-search) {
  height: 60rpx !important; 
  width: 446rpx !important; 
  border: none !important;
}

.search-wrapper :deep(.u-search__content) {
  height: 60rpx !important; 
  padding: 0 20rpx !important;
  background: #FFFFFF !important; 
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05) !important;
  border-radius: 30rpx !important;
}

.search-wrapper :deep(.u-search__input-wrap) {
  height: 60rpx !important;
  padding: 0 !important;
}

.search-wrapper :deep(.u-search__input) {
  height: 60rpx !important;
  line-height: 60rpx !important;
  font-size: 28rpx !important; 
  color: #333333 !important; 
  background-color: transparent !important;
}

.search-wrapper :deep(.u-search__input::placeholder) {
  color: #999999 !important; 
  font-size: 28rpx !important; 
}

.search-wrapper :deep(.u-search__action) {
  height: 60rpx !important;
  padding: 0 8rpx !important;
}

.search-wrapper :deep(.u-search__action-text) {
  font-size: 28rpx !important;
  color: #333333 !important;
}

</style>