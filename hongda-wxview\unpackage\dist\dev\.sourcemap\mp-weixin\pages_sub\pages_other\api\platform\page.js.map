{"version": 3, "file": "page.js", "sources": ["pages_sub/pages_other/api/platform/page.js"], "sourcesContent": ["import http from '@/utils/request.js';\r\n\r\n/**\r\n * 获取自定义页面详情\r\n * @param {number} id - 页面ID\r\n */\r\nexport function getPageDetailsApi(id) {\r\n    // [关键修改] 只保留相对路径\r\n    return http.get(`/page/${id}`);\r\n}"], "names": ["http"], "mappings": ";;AAMO,SAAS,kBAAkB,IAAI;AAElC,SAAOA,cAAAA,KAAK,IAAI,SAAS,EAAE,EAAE;AACjC;;"}