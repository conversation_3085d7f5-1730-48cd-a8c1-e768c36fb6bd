<template>
  <div class="app-container">
    <el-form :model="countryQueryParams" ref="countryQueryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="中文名称" prop="nameCn">
        <el-input v-model="countryQueryParams.nameCn" placeholder="请输入中文名称" clearable style="width: 200px"
                  @keyup.enter="handleCountryQuery"/>
      </el-form-item>
      <el-form-item label="所属大洲" prop="continent">
        <el-select v-model="countryQueryParams.continent" placeholder="请选择所属大洲" clearable style="width: 200px">
          <el-option v-for="dict in hongda_country_continent" :key="dict.value" :label="dict.label"
                     :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="countryQueryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option v-for="dict in sys_show_hide" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleCountryQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetCountryQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleCountryAdd" v-hasPermi="['content:country:add']">
          新增国别
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="countryMultiple" @click="handleCountryDelete"
                   v-hasPermi="['content:country:remove']">删除
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getCountryList"></right-toolbar>
    </el-row>

    <el-table v-loading="countryLoading" :data="countryList" @selection-change="handleCountrySelectionChange">
      <el-table-column type="selection" min-width="55" align="center" fixed/>
      <el-table-column label="显示顺序" align="center" prop="sortOrder" width="100" sortable/>
      <el-table-column label="国家信息" align="center" min-width="220">
        <template #default="scope">
          <div class="country-info-cell">
            <image-preview :src="scope.row.flagUrl" :width="40" :height="28" class="country-flag"/>
            <div class="country-names">
              <div class="name-cn">{{ scope.row.nameCn }}</div>
              <div class="name-en">{{ scope.row.nameEn }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属大洲" align="center" prop="continent" min-width="100">
        <template #default="scope">
          <dict-tag :options="hongda_country_continent" :value="scope.row.continent"/>
        </template>
      </el-table-column>
      <el-table-column label="简介" align="center" prop="summary" min-width="180" :show-overflow-tooltip="true"/>
      <el-table-column label="状态" align="center" prop="status" min-width="80">
        <template #default="scope">
          <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleCountryStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="FolderOpened" @click="handleManageDetails(scope.row)">管理详情
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleCountryUpdate(scope.row)"
                     v-hasPermi="['content:country:edit']">修改
          </el-button>
          <el-button link type="danger" icon="Delete" @click="handleCountryDelete(scope.row)"
                     v-hasPermi="['content:country:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="countryTotal > 0" :total="countryTotal" v-model:page="countryQueryParams.pageNum"
                v-model:limit="countryQueryParams.pageSize" @pagination="getCountryList"/>

    <el-dialog :title="countryTitle" v-model="countryOpen" width="80%" top="5vh" append-to-body
               :close-on-click-modal="false" destroy-on-close>
      <el-form ref="countryRef" :model="countryForm" :rules="countryRules" label-width="120px">
        <el-tabs v-model="activeCountryFormTab">
          <el-tab-pane label="核心信息" name="core">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="中文名称" prop="nameCn">
                  <el-input v-model="countryForm.nameCn" placeholder="请输入中文名称"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="英文名称" prop="nameEn">
                  <el-input v-model="countryForm.nameEn" placeholder="请输入英文名称"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属大洲" prop="continent">
                  <el-select v-model="countryForm.continent" placeholder="请选择所属大洲" style="width: 100%;">
                    <el-option v-for="dict in hongda_country_continent" :key="dict.value" :label="dict.label"
                               :value="dict.value"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="显示顺序" prop="sortOrder">
                  <el-input-number v-model="countryForm.sortOrder" controls-position="right" :min="0"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="一句话简介" prop="summary">
                  <el-input v-model="countryForm.summary" type="textarea" :rows="3" placeholder="请输入简介"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="国旗图片" prop="flagUrl">
                  <image-upload v-model="countryForm.flagUrl" :limit="1"/>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="列表封面图" prop="listCoverUrl">
                  <image-upload
                      v-model="countryForm.listCoverUrl"
                      :limit="1"
                      recommendation-text="推荐大小为504px * 288px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="详情封面大图" prop="detailsCoverUrl">
                  <image-upload
                      v-model="countryForm.detailsCoverUrl"
                      :limit="1"
                      recommendation-text="推荐大小为1125px * 630px"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="基本信息与简介" name="basic">
            <el-form-item label="国家简介">
              <el-input v-model="countryForm.introduction" type="textarea" :rows="12" placeholder="请输入国家简介"/>
            </el-form-item>
            <el-form-item label="基本信息项">
              <div class="dynamic-form-container">
                <div v-for="(item, index) in countryBasicInfoList" :key="`item-${index}`" class="dynamic-form-item">
                  <el-input v-model="item.key" placeholder="属性名 (如：首都)" class="dynamic-item-key"/>
                  <el-input v-model="item.value" placeholder="属性值 (如：新加坡)" class="dynamic-item-value"/>
                  <el-button @click="removeCountryBasicInfoItem(index)" :icon="proxy.icons.Delete" circle plain
                             type="danger" size="small"/>
                </div>
                <el-button @click="addCountryBasicInfoItem" :icon="proxy.icons.Plus" type="primary" plain
                           class="add-item-btn">添加基本信息项
                </el-button>
              </div>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="各项政策" name="policies">
            <el-form-item label="招商政策">
              <el-input v-model="countryForm.investmentPolicy" type="textarea" :rows="8" placeholder="请输入招商政策"/>
            </el-form-item>
            <el-form-item label="海关政策">
              <el-input v-model="countryForm.customsPolicy" type="textarea" :rows="8" placeholder="请输入海关政策"/>
            </el-form-item>
            <el-form-item label="税务政策">
              <el-input v-model="countryForm.taxPolicy" type="textarea" :rows="8" placeholder="请输入税务政策"/>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCountryForm">取 消</el-button>
          <el-button type="primary" @click="submitCountryForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="detailViewTitle" v-model="detailViewVisible" width="90%" top="5vh" append-to-body
               destroy-on-close>
      <el-tabs v-model="activeDetailTab" type="border-card">
        <el-tab-pane label="基本信息" name="basicInfo">
          <div class="basic-info-display">
            <h2 class="country-main-title">{{ selectedCountry?.nameCn }} ({{ selectedCountry?.nameEn }})</h2>
            <el-descriptions title="基本信息概览" :column="2" border v-if="parsedBasicInfo.length > 0">
              <el-descriptions-item v-for="item in parsedBasicInfo" :key="item.key" :label="item.key">
                {{ item.value }}
              </el-descriptions-item>
            </el-descriptions>
            <el-empty description="暂无基本信息项" v-else />
            <el-divider content-position="left">详细介绍</el-divider>
            <div class="text-content-section">
              <h3>国家简介</h3>
              <p class="content-paragraph">{{ selectedCountry?.introduction || '暂无内容' }}</p>
            </div>
            <div class="text-content-section">
              <h3>招商政策</h3>
              <p class="content-paragraph">{{ selectedCountry?.investmentPolicy || '暂无内容' }}</p>
            </div>
            <div class="text-content-section">
              <h3>海关政策</h3>
              <p class="content-paragraph">{{ selectedCountry?.customsPolicy || '暂无内容' }}</p>
            </div>
            <div class="text-content-section">
              <h3>税务政策</h3>
              <p class="content-paragraph">{{ selectedCountry?.taxPolicy || '暂无内容' }}</p>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="`关联园区管理 (${parkTotal})`" name="parks">
          <div class="integrated-management-pane">
            <el-form :model="parkQueryParams" ref="parkQueryRef" :inline="true" v-show="showParkSearch"
                     label-width="80px">
              <el-form-item label="园区名称" prop="name">
                <el-input v-model="parkQueryParams.name" placeholder="请输入园区名称" clearable
                          @keyup.enter="handleParkQuery"/>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select v-model="parkQueryParams.status" placeholder="园区状态" style="width: 120px" clearable>
                  <el-option v-for="dict in sys_show_hide" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleParkQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetParkQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleParkAdd" v-hasPermi="['content:park:add']">
                  新增园区
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="parkMultiple" @click="handleParkDelete"
                           v-hasPermi="['content:park:remove']">删除
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleParkExport"
                           v-hasPermi="['content:park:export']">导出
                </el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showParkSearch" @queryTable="getParkList"></right-toolbar>
            </el-row>
            <el-table v-loading="parkLoading" :data="parkList" @selection-change="handleParkSelectionChange"
                      height="55vh">
              <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="园区名称" prop="name" min-width="200" show-overflow-tooltip/>
              <el-table-column label="地理位置" prop="location" min-width="180" show-overflow-tooltip/>
              <el-table-column label="主要产业" prop="mainIndustries" min-width="200" show-overflow-tooltip/>
              <el-table-column label="状态" align="center" width="80">
                <template #default="scope">
                  <el-switch
                      v-model="scope.row.status"
                      active-value="0"
                      inactive-value="1"
                      @change="handleParkStatusChange(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="180" fixed="right">
                <template #default="scope">
                  <el-button link type="primary" icon="Edit" @click="handleParkUpdate(scope.row)"
                             v-hasPermi="['content:park:edit']">修改
                  </el-button>
                  <el-button link type="danger" icon="Delete" @click="handleParkDelete(scope.row)"
                             v-hasPermi="['content:park:remove']">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="parkTotal > 0" :total="parkTotal" v-model:page="parkQueryParams.pageNum"
                        v-model:limit="parkQueryParams.pageSize" @pagination="getParkList"/>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="`相关政策管理 (${policyTotal})`" name="policies">
          <div class="integrated-management-pane">
            <el-form :model="policyQueryParams" ref="policyQueryRef" :inline="true" v-show="showPolicySearch"
                     label-width="80px">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="policyQueryParams.title" placeholder="请输入文章标题" clearable
                          @keyup.enter="handlePolicyQuery"/>
              </el-form-item>
              <el-form-item label="政策大类" prop="policyType">
                <el-select v-model="policyQueryParams.policyType" placeholder="政策大类" style="width: 120px" clearable>
                  <el-option v-for="dict in country_policy_type" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select v-model="policyQueryParams.status" placeholder="状态" style="width: 120px" clearable>
                  <el-option v-for="dict in hongda_article_status" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handlePolicyQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetPolicyQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handlePolicyAdd"
                           v-hasPermi="['content:countryPolicy:add']">新增政策
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="policyMultiple" @click="handlePolicyDelete"
                           v-hasPermi="['content:countryPolicy:remove']">删除
                </el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showPolicySearch" @queryTable="getPolicyList"></right-toolbar>
            </el-row>
            <el-table v-loading="policyLoading" :data="policyList" @selection-change="handlePolicySelectionChange"
                      height="55vh">
              <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="文章标题" prop="title" min-width="250" show-overflow-tooltip/>
              <el-table-column label="政策大类" align="center" prop="policyType" width="120">
                <template #default="scope">
                  <dict-tag :options="country_policy_type" :value="scope.row.policyType"/>
                </template>
              </el-table-column>
              <el-table-column label="文章子分类" prop="categoryName" width="150"/>
              <el-table-column label="状态" align="center" prop="status" width="100">
                <template #default="scope">
                  <el-switch
                      v-model="scope.row.status"
                      active-value="1"
                      inactive-value="2"  @change="handlePolicyStatusChange(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" prop="createTime" width="180" sortable/>
              <el-table-column label="操作" align="center" width="220" fixed="right">
                <template #default="scope">
                  <el-button link type="primary" icon="View" @click="handlePolicyPreview(scope.row)">预览</el-button>
                  <el-button link type="primary" icon="Edit" @click="handlePolicyUpdate(scope.row)"
                             v-hasPermi="['content:countryPolicy:edit']">修改
                  </el-button>
                  <el-button link type="danger" icon="Delete" @click="handlePolicyDelete(scope.row)"
                             v-hasPermi="['content:countryPolicy:remove']">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="policyTotal > 0" :total="policyTotal" v-model:page="policyQueryParams.pageNum"
                        v-model:limit="policyQueryParams.pageSize" @pagination="getPolicyList"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog :title="parkTitle" v-model="parkOpen" width="80%" top="5vh" append-to-body
               :close-on-click-modal="false" destroy-on-close class="integrated-form-dialog">
      <el-form ref="parkRef" :model="parkForm" :rules="parkRules" label-width="100px">
        <el-tabs>
          <el-tab-pane label="核心信息">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="园区名称" prop="name">
                  <el-input v-model="parkForm.name" placeholder="请输入园区名称"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属国家">
                  <el-input :value="selectedCountry?.nameCn" disabled/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地理位置" prop="location">
                  <el-input v-model="parkForm.location" placeholder="例如：新加坡西部裕廊区"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="主要产业" prop="mainIndustries">
                  <el-input v-model="parkForm.mainIndustries" type="textarea" :rows="2"
                            placeholder="请输入主要产业，用逗号分隔"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="园区封面图" prop="coverImageUrl">
                  <image-upload v-model="parkForm.coverImageUrl" :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="详细内容">
            <el-form-item label="园区简介" prop="summary">
              <div class="editor-container">
                <editor v-model="parkForm.summary" :min-height="200"/>
              </div>
            </el-form-item>
            <el-form-item label="详细介绍" prop="content">
              <div class="editor-container">
                <editor v-model="parkForm.content" :min-height="200"/>
              </div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelParkForm">取 消</el-button>
          <el-button type="primary" @click="submitParkForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
        :title="policyTitle"
        v-model="policyOpen"
        width="80%"
        top="5vh"
        append-to-body
        :close-on-click-modal="false"
        destroy-on-close
        class="policy-dialog"
        @close="handlePolicyDialogClose"
        :key="policyDialogKey"
    >
      <el-form ref="policyRef" :model="policyForm" :rules="policyRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联国家" prop="countryId">
              <el-input :value="selectedCountry?.nameCn" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="政策大类" prop="policyType">
              <el-select v-model="policyForm.policyType" placeholder="请选择政策大类" style="width: 100%;">
                <el-option v-for="dict in country_policy_type" :key="dict.value" :label="dict.label"
                           :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文章子分类" prop="categoryName">
              <el-input v-model="policyForm.categoryName" placeholder="请输入文章子分类"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文章标题" prop="title">
              <el-input v-model="policyForm.title" placeholder="请输入文章标题"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文章摘要" prop="summary">
              <el-input v-model="policyForm.summary" type="textarea" :rows="3" placeholder="请输入摘要内容"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文章正文" prop="content">
              <div class="editor-container">
                <editor v-model="policyForm.content" :min-height="192"/>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示顺序" prop="sortOrder">
              <el-input-number v-model="policyForm.sortOrder" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelPolicyForm">取 消</el-button>
          <el-button type="primary" @click="submitPolicyForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="policyPreviewVisible" width="auto" :show-close="false" :modal="true" :lock-scroll="true"
               align-center class="device-preview-dialog">
      <div class="preview-wrapper">
        <div class="preview-container" :style="{ width: previewWidth + 'px', height: previewHeight + 'px' }">
          <SimplePagePreview v-if="policyPreviewVisible" :page="currentPolicyForPreview"/>
        </div>
        <el-button class="preview-close-btn" circle :icon="proxy.icons.Close" @click="policyPreviewVisible = false"/>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="CountryIntegratedManagement">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, watch, computed, nextTick } from 'vue';
import SimplePagePreview from '@/components/SimplePagePreview/index.vue';
import { listCountry, getCountry, delCountry, addCountry, updateCountry } from "@/api/content/country";
import { listPark, getPark, delPark, addPark, updatePark } from "@/api/content/park";
import {
  listCountryPolicy,
  getCountryPolicy,
  delCountryPolicy,
  addCountryPolicy,
  updateCountryPolicy
} from "@/api/content/countryPolicy";
import { Delete, Plus, ArrowDown, Close } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
proxy.icons = { Delete, Plus, Close };

const {
  hongda_country_continent,
  sys_show_hide,
  country_policy_type,
  hongda_article_status
} = proxy.useDict(
    'hongda_country_continent', 'sys_show_hide', 'country_policy_type', 'hongda_article_status'
);

// ======================================================================================
// ============================= 1. 国别管理 (Country) ================================
// ======================================================================================
const countryList = ref([]);
const countryOpen = ref(false);
const countryLoading = ref(true);
const showSearch = ref(true);
const countryIds = ref([]);
const countrySingle = ref(true);
const countryMultiple = ref(true);
const countryTotal = ref(0);
const countryTitle = ref("");
const activeCountryFormTab = ref("core");
const countryBasicInfoList = ref([]);

const countryData = reactive({
  countryForm: {},
  countryQueryParams: { pageNum: 1, pageSize: 10, nameCn: null, continent: null, status: null },
  countryRules: {
    nameCn: [{ required: true, message: "中文名称不能为空", trigger: "blur" }],
    continent: [{ required: true, message: "所属大洲不能为空", trigger: "change" }]
  }
});
const { countryQueryParams, countryForm, countryRules } = toRefs(countryData);

function getCountryList() {
  countryLoading.value = true;
  listCountry(countryQueryParams.value).then(r => {
    countryList.value = r.rows;
    countryTotal.value = r.total;
    countryLoading.value = false;
  });
}

function handleCountryQuery() {
  countryQueryParams.value.pageNum = 1;
  getCountryList();
}

function resetCountryQuery() {
  proxy.resetForm("countryQueryRef");
  handleCountryQuery();
}

function cancelCountryForm() {
  countryOpen.value = false;
  resetCountryForm();
}

function resetCountryForm() {
  countryForm.value = {
    id: null, nameCn: null, nameEn: null, summary: null, continent: null,
    flagUrl: null, introduction: null, basicInfoJson: null, investmentPolicy: null,
    customsPolicy: null, taxPolicy: null, status: "0", sortOrder: 0
  };
  countryBasicInfoList.value = [];
  proxy.resetForm("countryRef");
}

function addCountryBasicInfoItem() {
  countryBasicInfoList.value.push({ key: '', value: '' });
}

function removeCountryBasicInfoItem(index) {
  countryBasicInfoList.value.splice(index, 1);
}

function handleCountryAdd() {
  resetCountryForm();
  activeCountryFormTab.value = "core";
  countryOpen.value = true;
  countryTitle.value = "添加国别信息";
}

function handleCountryUpdate(row) {
  resetCountryForm();
  activeCountryFormTab.value = "core";
  const _id = row.id || countryIds.value[0];
  getCountry(_id).then(response => {
    countryForm.value = response.data;
    try {
      countryBasicInfoList.value = JSON.parse(countryForm.value.basicInfoJson) || [];
    } catch (e) {
      countryBasicInfoList.value = [];
    }
    countryOpen.value = true;
    countryTitle.value = "修改国别信息";
  });
}

async function submitCountryForm() {
  try {
    const valid = await proxy.$refs["countryRef"].validate();
    if (!valid) return;

    const validItems = countryBasicInfoList.value.filter(item => item.key?.trim() && item.value?.trim());
    countryForm.value.basicInfoJson = JSON.stringify(validItems);

    if (countryForm.value.id != null) {
      await updateCountry(countryForm.value);
    } else {
      await addCountry(countryForm.value);
    }
    proxy.$modal.msgSuccess(countryForm.value.id ? "修改成功" : "新增成功");
    countryOpen.value = false;
    getCountryList();
  } catch (error) {
    console.error("提交国别表单失败:", error);
  }
}

function handleCountryDelete(row) {
  const _ids = row.id ? [row.id] : countryIds.value;
  proxy.$modal.confirm('是否确认删除选中的国别信息？').then(() => delCountry(_ids))
      .then(() => {
        getCountryList();
        proxy.$modal.msgSuccess("删除成功");
      }).catch(() => {});
}

function handleCountrySelectionChange(selection) {
  countryIds.value = selection.map(item => item.id);
  countrySingle.value = selection.length !== 1;
  countryMultiple.value = !selection.length;
}

const parsedBasicInfo = computed(() => {
  if (!selectedCountry.value?.basicInfoJson) return [];
  try {
    const data = JSON.parse(selectedCountry.value.basicInfoJson);
    return Array.isArray(data) ? data.filter(item => item.key && item.value) : [];
  } catch (e) {
    console.error("解析国别基本信息JSON失败:", e);
    return [];
  }
});

/**
 * 处理国别状态变更
 */
function handleCountryStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm(`确认要"${text}"【${row.nameCn}】吗？`).then(function() {
    return updateCountry({ id: row.id, status: row.status });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    row.status = row.status === "0" ? "1" : "0";
  });
}

/**
 * 处理园区状态变更
 */
function handleParkStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm(`确认要"${text}"【${row.name}】吗？`).then(function() {
    return updatePark({ id: row.id, status: row.status });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    row.status = row.status === "0" ? "1" : "0";
  });
}

/**
 * 处理政策状态变更
 */
function handlePolicyStatusChange(row) {
  // 当状态值在 "1" (发布) 和 "2" (下架) 之间切换时，更新提示文本
  let text = row.status === "1" ? "发布" : "下架";
  proxy.$modal.confirm(`确认要将文章【${row.title}】"${text}"吗？`).then(function() {
    return updateCountryPolicy({ articleId: row.articleId, status: row.status });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function() {
    // 如果API调用失败，将开关恢复到原始状态
    row.status = row.status === "1" ? "2" : "1";
  });
}

// ======================================================================================
// ============================= 2. 集成式管理 (Integrated) =============================
// ======================================================================================
const detailViewVisible = ref(false);
const detailViewTitle = ref("");
const selectedCountry = ref(null);
const activeDetailTab = ref('basicInfo');

function handleManageDetails(row) {
  selectedCountry.value = row;
  detailViewTitle.value = `"${row.nameCn}" 详情与关联管理`;
  activeDetailTab.value = 'basicInfo';

  parkList.value = [];
  policyList.value = [];
  parkTotal.value = 0;
  policyTotal.value = 0;

  detailViewVisible.value = true;

  getParkList();
  getPolicyList();
}

watch(activeDetailTab, (newTab) => {
  if (!detailViewVisible.value || !selectedCountry.value) return;
  if (newTab === 'parks' && parkList.value.length === 0) {
    getParkList();
  } else if (newTab === 'policies' && policyList.value.length === 0) {
    getPolicyList();
  }
});

// ======================================================================================
// ============================= 3. 园区管理 (Park) ===================================
// ======================================================================================
const parkList = ref([]);
const parkOpen = ref(false);
const parkLoading = ref(false);
const parkTotal = ref(0);
const parkTitle = ref("");
const showParkSearch = ref(true);
const parkSelectionIds = ref([]);
const parkMultiple = computed(() => parkSelectionIds.value.length === 0);

const parkData = reactive({
  parkForm: {},
  parkQueryParams: { pageNum: 1, pageSize: 10, name: null, status: null },
  parkRules: {
    name: [{ required: true, message: "园区名称不能为空", trigger: "blur" }]
  }
});
const { parkQueryParams, parkForm, parkRules } = toRefs(parkData);

function getParkList() {
  parkLoading.value = true;
  parkQueryParams.value.countryId = selectedCountry.value?.id;
  listPark(parkQueryParams.value).then(r => {
    parkList.value = r.rows;
    parkTotal.value = r.total;
    parkLoading.value = false;
  });
}

function handleParkQuery() {
  parkQueryParams.value.pageNum = 1;
  getParkList();
}

function resetParkQuery() {
  proxy.resetForm("parkQueryRef");
  handleParkQuery();
}

function handleParkAdd() {
  resetParkForm();
  parkForm.value.countryId = selectedCountry.value?.id;
  parkOpen.value = true;
  parkTitle.value = `为 "${selectedCountry.value?.nameCn}" 新增园区`;
}

function handleParkUpdate(row) {
  resetParkForm();
  getPark(row.id).then(r => {
    parkForm.value = r.data;
    parkOpen.value = true;
    parkTitle.value = "修改园区信息";
  });
}

async function submitParkForm() {
  try {
    const valid = await proxy.$refs["parkRef"].validate();
    if (!valid) return;

    if (parkForm.value.id != null) {
      await updatePark(parkForm.value);
    } else {
      await addPark(parkForm.value);
    }
    proxy.$modal.msgSuccess(parkForm.value.id ? "修改成功" : "新增成功");
    parkOpen.value = false;
    getParkList();
  } catch (error) {
    console.error("提交园区表单失败:", error);
  }
}

function handleParkDelete(row) {
  const _ids = row.id ? [row.id] : parkSelectionIds.value;
  proxy.$modal.confirm('确认删除选中的园区吗？').then(() => delPark(_ids))
      .then(() => {
        getParkList();
        proxy.$modal.msgSuccess("删除成功");
      }).catch(() => {});
}

function handleParkExport() {
  proxy.download('content/park/export', {
    ...parkQueryParams.value,
    countryId: selectedCountry.value?.id
  }, `park_${new Date().getTime()}.xlsx`);
}

function handleParkSelectionChange(selection) {
  parkSelectionIds.value = selection.map(item => item.id);
}

function resetParkForm() {
  parkForm.value = {
    id: null, countryId: null, name: null, location: null, mainIndustries: null,
    summary: '', coverImageUrl: null, content: '', status: "0"
  };
  proxy.resetForm("parkRef");
}

function cancelParkForm() {
  parkOpen.value = false;
  resetParkForm();
}

// ======================================================================================
// ============================= 4. 政策管理 (Policy) =================================
// ======================================================================================
const policyList = ref([]);
const policyOpen = ref(false);
const policyLoading = ref(false);
const policyTotal = ref(0);
const policyTitle = ref("");
const showPolicySearch = ref(false);
const policySelectionIds = ref([]);
const policyMultiple = computed(() => policySelectionIds.value.length === 0);
const policyDialogKey = ref(0);

const policyData = reactive({
  policyForm: {},
  policyQueryParams: {pageNum: 1, pageSize: 10, title: null, policyType: null, status: null},
  policyRules: {
    title: [{required: true, message: "文章标题不能为空", trigger: "blur"}],
    policyType: [{required: true, message: "政策大类不能为空", trigger: "change"}],
    categoryName: [{required: true, message: "文章子分类不能为空", trigger: "blur"}]
  }
});
const {policyQueryParams, policyForm, policyRules} = toRefs(policyData);

function getPolicyList() {
  policyLoading.value = true;
  policyQueryParams.value.countryId = selectedCountry.value?.id;
  listCountryPolicy(policyQueryParams.value).then(r => {
    policyList.value = r.rows;
    policyTotal.value = r.total;
    policyLoading.value = false;
  });
}

function handlePolicyQuery() {
  policyQueryParams.value.pageNum = 1;
  getPolicyList();
}

function resetPolicyQuery() {
  proxy.resetForm("policyQueryRef");
  handlePolicyQuery();
}

function handlePolicyAdd() {
  resetPolicyForm();
  policyDialogKey.value++;
  policyForm.value.countryId = selectedCountry.value?.id;
  nextTick(() => {
    policyOpen.value = true;
    policyTitle.value = `为 "${selectedCountry.value?.nameCn}" 新增政策`;
  });
}

function handlePolicyUpdate(row) {
  resetPolicyForm();
  policyDialogKey.value++;

  getCountryPolicy(row.articleId).then(r => {
    policyForm.value = r.data;
    nextTick(() => {
      policyOpen.value = true;
      policyTitle.value = "修改政策信息";
    });
  }).catch(() => {
    proxy.$modal.msgError("获取数据失败");
  });
}

function submitPolicyForm() {
  proxy.$refs["policyRef"].validate(valid => {
    if (valid) {
      if (policyForm.value.articleId != null) {
        updateCountryPolicy(policyForm.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          handlePolicyClose();
          getPolicyList();
        }).catch(() => {
          proxy.$modal.msgError("修改失败");
        });
      } else {
        addCountryPolicy(policyForm.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          handlePolicyClose();
          getPolicyList();
        }).catch(() => {
          proxy.$modal.msgError("新增失败");
        });
      }
    }
  });
}

function handlePolicyDelete(row) {
  const _ids = row.articleId ? [row.articleId] : policySelectionIds.value;
  proxy.$modal.confirm('确认删除选中的政策吗？').then(() => delCountryPolicy(_ids))
      .then(() => {
        getPolicyList();
        proxy.$modal.msgSuccess("删除成功");
      }).catch(() => {
  });
}

function handlePolicySelectionChange(selection) {
  policySelectionIds.value = selection.map(item => item.articleId);
}

function handlePolicyClose() {
  policyOpen.value = false;
  setTimeout(() => {
    if (proxy.$refs["policyRef"]) {
      proxy.$refs["policyRef"].clearValidate();
    }
    resetPolicyForm();
    policyDialogKey.value++;
  }, 100);
}

function handlePolicyDialogClose() {
  if (proxy.$refs["policyRef"]) {
    proxy.$refs["policyRef"].clearValidate();
  }
  resetPolicyForm();
  policyDialogKey.value++;
}

function resetPolicyForm() {
  policyForm.value = {
    articleId: null,
    countryId: null,
    policyType: null,
    categoryName: null,
    title: null,
    summary: null,
    content: null,
    status: "1",
    sortOrder: 0
  };
  nextTick(() => {
    if (proxy.$refs["policyRef"]) {
      proxy.$refs["policyRef"].resetFields();
    }
  });
}

function cancelPolicyForm() {
  handlePolicyClose();
}

// --- 设备预览功能 ---
const policyPreviewVisible = ref(false);
const currentPolicyForPreview = ref(null);
const previewWidth = 390;
const previewHeight = 844;

function handlePolicyPreview(row) {
  if (!row || !row.content) {
    proxy.$modal.msgWarning('该文章暂无内容');
    return;
  }
  currentPolicyForPreview.value = { ...row };
  policyPreviewVisible.value = true;
}

// --- 页面初始化 ---
onMounted(() => {
  getCountryList();
});
</script>

<style scoped>
/* 通用样式 */
.integrated-management-pane {
  padding: 10px;
}

.country-info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 0;
}

.country-flag {
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.country-names {
  text-align: left;
}

.name-cn {
  font-weight: 600;
}

.name-en {
  font-size: 12px;
  color: #909399;
}

/* 动态表单项 */
.dynamic-form-container {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  width: 100%;
}

.dynamic-form-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.dynamic-item-key {
  width: 200px;
  flex-shrink: 0;
}

.dynamic-item-value {
  flex: 1;
}

.add-item-btn {
  width: 100%;
  margin-top: 8px;
}

/* 详情Tab内的样式 */
.basic-info-display {
  padding: 10px 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.country-main-title {
  text-align: center;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 600;
}

.text-content-section {
  margin-top: 20px;
}

.text-content-section h3 {
  font-size: 16px;
  margin-bottom: 12px;
  color: #303133;
  border-left: 4px solid #409EFF;
  padding-left: 8px;
}

.content-paragraph {
  line-height: 1.8;
  color: #606266;
  white-space: pre-wrap;
  text-indent: 2em;
}

/* 最终版设备预览对话框样式 */
:deep(.device-preview-dialog.el-dialog) {
  background-color: transparent !important;
  box-shadow: none !important;
  --el-dialog-padding-primary: 0;
}

:deep(.device-preview-dialog .el-dialog__header) {
  display: none;
}

:deep(.device-preview-dialog .el-dialog__body) {
  padding: 0;
}

.preview-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-container {
  overflow: hidden;
  border-radius: 40px;
  box-shadow: inset 0 0 0 2px #555,
  inset 0 0 0 6px #000,
  0 10px 40px rgba(0, 0, 0, 0.3);
}

.preview-close-btn {
  margin-top: 24px;
  --el-button-size: 50px;
  --el-button-text-color: #ffffff;
  --el-button-bg-color: rgba(0, 0, 0, 0.4);
  --el-button-border-color: rgba(255, 255, 255, 0.3);
  --el-button-hover-text-color: #ffffff;
  --el-button-hover-bg-color: rgba(0, 0, 0, 0.6);
  --el-button-hover-border-color: rgba(255, 255, 255, 0.5);
  font-size: 24px;
}

/* 修复：园区管理弹窗样式 */
.integrated-form-dialog :deep(.el-dialog) {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.integrated-form-dialog :deep(.el-dialog__body) {
  flex: 1;
  overflow-y: auto;
}

/* 修复富文本编辑器容器样式 */
.policy-dialog :deep(.el-dialog) {
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.policy-dialog :deep(.el-dialog__body) {
  flex: 1;
  overflow-y: auto;
  padding: 20px 20px 0;
}

.editor-container {
  border: 1px solid #ccc;
  z-index: 100;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.editor-container :deep(.w-e-text-container) {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.editor-container :deep(.w-e-toolbar) {
  border-bottom: 1px solid #ccc;
  position: relative;
  z-index: 1;
}

.editor-container :deep(.w-e-text) {
  border-top: none !important;
  position: relative;
  z-index: 1;
}

/* 确保富文本编辑器不会超出容器 */
.editor-container :deep(.w-e-toolbar),
.editor-container :deep(.w-e-text-container),
.editor-container :deep(.w-e-text) {
  box-sizing: border-box;
  max-width: 100%;
}

/* 修复可能的下拉菜单层级问题 */
.editor-container :deep(.w-e-menu-tooltip),
.editor-container :deep(.w-e-menu-panel) {
  z-index: 2020 !important;
}

</style>