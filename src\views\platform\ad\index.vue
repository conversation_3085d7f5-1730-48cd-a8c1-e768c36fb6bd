<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="广告标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入广告标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告位代码" prop="positionCode">
        <el-select v-model="queryParams.positionCode" placeholder="请选择广告位代码" clearable style="width: 200px">
          <el-option
              v-for="dict in hongda_ad_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px;">
          <el-option
              v-for="dict in hongda_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['platform:ad:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['platform:ad:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['platform:ad:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['platform:ad:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="adList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="广告ID" align="center" prop="id" />
      <el-table-column label="广告标题" align="center" prop="title" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="广告图片" align="center" prop="imageUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="广告位" align="center" prop="positionCode">
        <template #default="scope">
          <dict-tag :options="hongda_ad_position" :value="scope.row.positionCode"/>
        </template>
      </el-table-column>

      <el-table-column label="关联目标" align="center" width="220" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.pageId">
            <el-tag :type="scope.row.pageType === 'RICH_TEXT' ? 'warning' : 'success'">页面</el-tag>
            <span style="margin-left: 5px;">
              {{ scope.row.pageType === 'RICH_TEXT' ? scope.row.pageTitle : scope.row.pageTargetUrl }}
            </span>
          </div>
          <div v-else-if="scope.row.linkUrl">
            <el-tag type="primary">链接</el-tag>
            <span style="margin-left: 5px;">{{ scope.row.linkUrl }}</span>
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>

      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-hasPermi="['platform:ad:edit']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['platform:ad:edit']">修改</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['platform:ad:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="adRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="广告标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入广告标题" />
        </el-form-item>
        <el-form-item label="广告位" prop="positionCode">
          <el-select v-model="form.positionCode" placeholder="请选择广告位">
            <el-option
                v-for="dict in hongda_ad_position"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="广告图片" prop="imageUrl">
          <image-upload v-model="form.imageUrl"/>
          <div v-if="imageSizeHint" class="form-help-text" style="color: #E6A23C;">
            <el-icon><Warning /></el-icon> {{ imageSizeHint }}
          </div>
        </el-form-item>

        <el-form-item label="关联页面" prop="pageId">
          <el-select
              v-model="form.pageId"
              placeholder="请选择页面"
              filterable
              clearable
              style="width: 100%;"
              :disabled="!!form.linkUrl"
              @change="handlePageChange"
          >
            <el-option
                v-for="item in pageOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
            />
          </el-select>
          <div class="form-help-text">
            <strong>首选方案。</strong>用于链接到【页面管理】中创建的可复用页面。
          </div>
        </el-form-item>

        <el-form-item label="页面链接" v-if="selectedPageUrl">
          <div class="page-link-container">
            <el-tooltip :content="selectedPageUrl" placement="top" :show-after="500">
              <span class="link-text-ellipsis">{{ selectedPageUrl }}</span>
            </el-tooltip>
            <el-button
                link
                type="primary"
                :icon="DocumentCopy"
                @click="copyPageUrl"
                class="copy-btn"
            >
              复制
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="临时/外部链接" prop="linkUrl">
          <el-input
              v-model="form.linkUrl"
              type="textarea"
              placeholder="备用项，用于一次性外部链接"
              :disabled="!!form.pageId"
              @input="handleLinkUrlInput"
          />
          <div class="form-help-text">
            <strong>备用方案。</strong>用于跳转到外部网站或一次性H5页。<br/>
            <span style="color: #F56C6C;">注意：请务必填写以 https:// 或 http:// 开头的完整链接地址。</span>
          </div>
        </el-form-item>

        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ad">
import { ref, reactive, toRefs, computed, watch } from "vue";
import { DocumentCopy, Warning } from '@element-plus/icons-vue';
import { listAd, getAd, delAd, addAd, updateAd } from "@/api/platform/ad";
import { listPage } from "@/api/platform/page";

const { proxy } = getCurrentInstance();
const { hongda_common_status, hongda_ad_position } = proxy.useDict('hongda_common_status', 'hongda_ad_position');

const adList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const pageOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    positionCode: null,
    status: null,
  },
  // [MODIFIED] 使用自定义校验规则
  rules: {
    title: [ { required: true, message: "广告标题不能为空", trigger: "blur" } ],
    positionCode: [ { required: true, message: "广告位代码不能为空", trigger: "change" } ],
    imageUrl: [ { required: true, message: "广告图片链接不能为空", trigger: "blur" } ],
    pageId: [{ validator: (rule, value, callback) => {
        if (!value && !data.form.linkUrl) {
          callback(new Error("关联页面和外部链接必须填写一个"));
        } else {
          callback();
        }
      }, trigger: "change" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

const imageSizeHint = computed(() => {
  if (!form.value.positionCode) return '请先选择广告位';
  switch (form.value.positionCode) {
    case 'HOME_BANNER': return '推荐尺寸：1153px * 444px';
    case 'SPLASH_SCREEN': return '推荐尺寸：759px * 1350px';
    default: return '';
  }
});

// [MODIFIED] 优化 selectedPageUrl 计算属性，以处理富文本页面
const selectedPageUrl = computed(() => {
  if (form.value.pageId && pageOptions.value.length > 0) {
    const selectedPage = pageOptions.value.find(item => item.id === form.value.pageId);
    if (selectedPage) {
      if (selectedPage.pageType === 'RICH_TEXT') {
        return `这是一个自定义页面，没有直接链接。`;
      }
      return selectedPage.targetUrl;
    }
  }
  return '';
});

/** 复制页面链接到剪贴板 */
function copyPageUrl() {
  if (!selectedPageUrl.value || selectedPageUrl.value.includes('没有直接链接')) return;
  navigator.clipboard.writeText(selectedPageUrl.value).then(() => {
    proxy.$modal.msgSuccess("链接已复制到剪贴板！");
  }).catch(err => {
    proxy.$modal.msgError("链接复制失败！");
  });
}

/** 当用户选择一个页面时，清空外部链接并触发校验 */
function handlePageChange(selectedId) {
  if (selectedId) {
    form.value.linkUrl = '';
  }
  // 手动触发校验
  proxy.$refs.adRef.validateField('pageId');
}

/** 当用户手动输入自定义链接时，清空关联页面并触发校验 */
function handleLinkUrlInput(value) {
  if (value) {
    form.value.pageId = null;
  }
  // 手动触发校验
  proxy.$refs.adRef.validateField('pageId');
}

/** 获取页面列表 */
function getPageList() {
  return listPage({ pageNum: 1, pageSize: 9999 }).then(response => {
    pageOptions.value = response.rows;
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    id: null,
    title: null,
    positionCode: null,
    imageUrl: null,
    pageId: null,
    linkUrl: null,
    sortOrder: 0,
    status: 1,
  };
  proxy.resetForm("adRef");
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleStatusChange(row) {
  // 检查广告位是否为需要特殊校验的“首页轮播”
  if (row.positionCode === 'HOME_BANNER') {

    // 逻辑分支1：当用户尝试“启用”一个轮播广告时
    if (row.status === 1) {
      // 1.1 检查是否会超出最大数量限制（5个）
      const activeBanners = adList.value.filter(ad => ad.positionCode === 'HOME_BANNER' && ad.status === 1);
      if (activeBanners.length > 5) {
        proxy.$modal.msgError("操作失败：首页轮播广告最多只能启用5个！");
        row.status = 0; // 恢复开关状态
        return; // 阻止后续操作
      }
    }

    // 逻辑分支2：当用户尝试“禁用”一个轮播广告时
    else if (row.status === 0) {
      // 2.1 检查是否会低于最小数量限制（1个）
      const activeBanners = adList.value.filter(ad => ad.positionCode === 'HOME_BANNER' && ad.status === 1);
      if (activeBanners.length < 1) {
        proxy.$modal.msgError("操作失败：首页轮播广告最少需要保留1个启用！");
        row.status = 1; // 恢复开关状态
        return; // 阻止后续操作
      }
    }
  }

  // 对于所有检查通过的广告（包括非轮播广告），执行标准的状态更新流程
  let text = row.status === 1 ? "启用" : "禁用";
  proxy.$modal.confirm(`确认要"${text}"广告【${row.title}】吗？`).then(function() {
    return updateAd({ id: row.id, status: row.status });
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    getList(); // 刷新列表以确保数据一致
  }).catch(function() {
    // API调用失败时，恢复开关状态
    row.status = row.status === 1 ? 0 : 1;
  });
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getPageList();
  open.value = true;
  title.value = "添加广告";
}

function handleUpdate(row) {
  reset();
  getPageList().then(() => {
    const _id = row.id || ids.value[0];
    getAd(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改广告";
    });
  });
}

function submitForm() {
  proxy.$refs["adRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAd(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAd(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除广告管理编号为"' + _ids + '"的数据项？').then(function() {
    return delAd(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('platform/ad/export', { ...queryParams.value }, `ad_${new Date().getTime()}.xlsx`);
}

function getList() {
  loading.value = true;
  listAd(queryParams.value).then(response => {
    adList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

getList();
</script>

<style scoped>
.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.5;
}
.page-link-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 32px;
  padding: 0 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
.link-text-ellipsis {
  flex-grow: 1;
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}
.copy-btn {
  flex-shrink: 0;
}
</style>