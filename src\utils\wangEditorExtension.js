import { Boot } from '@wangeditor/editor';

/**
 * 扩展图片模块以支持自定义 data-oss-object-name 属性
 * 这个函数需要在编辑器创建之前调用
 */
export function extendImageModuleForOSS() {
    let isExtended = false;

    // 避免重复扩展
    if (isExtended) {
        console.log('[WangEditor] 图片模块已经扩展过了');
        return;
    }

    try {
        // 方法1：尝试通过模块名获取
        const imageModule = Boot.getModule('image');

        if (imageModule && imageModule.config) {
            console.log('[WangEditor] 找到图片模块，开始扩展...');
            extendImageConfig(imageModule.config);
            isExtended = true;
        } else {
            console.warn('[WangEditor] 方法1失败，尝试方法2...');

            // 方法2：通过已注册模块查找
            const registeredModules = Boot.getRegisteredModules ? Boot.getRegisteredModules() : [];
            const foundImageModule = registeredModules.find(module =>
                module.moduleName === 'image' ||
                module.moduleName === '@wangeditor/plugin-image' ||
                (module.config && module.config.type === 'image')
            );

            if (foundImageModule && foundImageModule.config) {
                console.log('[WangEditor] 通过方法2找到图片模块，开始扩展...');
                extendImageConfig(foundImageModule.config);
                isExtended = true;
            } else {
                console.error('[WangEditor] 无法找到图片模块，扩展失败');
            }
        }
    } catch (error) {
        console.error('[WangEditor] 扩展图片模块时发生错误:', error);
    }
}

/**
 * 具体扩展图片配置的逻辑
 */
function extendImageConfig(config) {
    // 保存原始的 toHtml 和 parseHtml 方法
    const originalToHtml = config.toHtml;
    const originalParseHtml = config.parseHtml;

    // 扩展 toHtml 方法 - 将节点转换为 HTML 时保留自定义属性
    config.toHtml = function(elemNode, children, editor) {
        console.log('[WangEditor toHtml] 处理图片节点:', elemNode);

        // 先调用原始方法获取基础HTML
        let html = '';
        if (originalToHtml) {
            html = originalToHtml.call(this, elemNode, children, editor);
        } else {
            // 如果没有原始方法，提供一个基础实现
            const { src, alt = '', width, height } = elemNode;
            let attrs = `src="${src}" alt="${alt}"`;
            if (width) attrs += ` width="${width}"`;
            if (height) attrs += ` height="${height}"`;
            html = `<img ${attrs}>`;
        }

        // 如果节点包含 OSS 对象名，添加到 HTML 中
        if (elemNode.dataOssObjectName && typeof html === 'string') {
            console.log('[WangEditor toHtml] 添加 data-oss-object-name:', elemNode.dataOssObjectName);

            // 使用正则表达式在 img 标签中添加自定义属性
            html = html.replace(
                /(<img[^>]*?)(\s*\/?>)/,
                `$1 data-oss-object-name="${elemNode.dataOssObjectName}"$2`
            );
        }

        console.log('[WangEditor toHtml] 最终HTML:', html);
        return html;
    };

    // 扩展 parseHtml 方法 - 从 HTML 解析节点时读取自定义属性
    config.parseHtml = function(domElem, children, editor) {
        console.log('[WangEditor parseHtml] 解析DOM元素:', domElem);

        // 先调用原始方法获取基础节点
        let result = null;
        if (originalParseHtml) {
            result = originalParseHtml.call(this, domElem, children, editor);
        } else if (domElem.tagName && domElem.tagName.toLowerCase() === 'img') {
            // 如果没有原始方法，提供一个基础实现
            result = {
                type: 'image',
                src: domElem.getAttribute('src') || '',
                alt: domElem.getAttribute('alt') || '',
                children: [{ text: '' }]
            };
        }

        // 如果成功解析了图片节点，尝试提取 OSS 对象名
        if (result && domElem.tagName && domElem.tagName.toLowerCase() === 'img') {
            const dataOssObjectName = domElem.getAttribute('data-oss-object-name');
            if (dataOssObjectName) {
                console.log('[WangEditor parseHtml] 找到 data-oss-object-name:', dataOssObjectName);
                result.dataOssObjectName = dataOssObjectName;
            }
        }

        console.log('[WangEditor parseHtml] 解析结果:', result);
        return result;
    };

    console.log('[WangEditor] 图片模块扩展完成');
}