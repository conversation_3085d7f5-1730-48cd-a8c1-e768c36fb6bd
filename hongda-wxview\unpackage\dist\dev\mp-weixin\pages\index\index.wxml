<view class="page-container data-v-1cf27b2a"><scroll-view class="main-scroll-view data-v-1cf27b2a" scroll-y bindscrolltolower="{{d}}"><header-component class="data-v-1cf27b2a" u-i="1cf27b2a-0" bind:__l="__l"/><view class="data-v-1cf27b2a"><banner-component class="data-v-1cf27b2a" u-i="1cf27b2a-1" bind:__l="__l"/></view><view class="data-v-1cf27b2a"><quick-navigation-component class="data-v-1cf27b2a" u-i="1cf27b2a-2" bind:__l="__l"/></view><view class="data-v-1cf27b2a"><country-highlight-component class="data-v-1cf27b2a" u-i="1cf27b2a-3" bind:__l="__l"/></view><view class="data-v-1cf27b2a"><event-promotion-component class="data-v-1cf27b2a" u-i="1cf27b2a-4" bind:__l="__l"/></view><view class="data-v-1cf27b2a"><news-list-component class="data-v-1cf27b2a" u-i="1cf27b2a-5" bind:__l="__l"/></view><view class="data-v-1cf27b2a"><activity-grid-component class="r data-v-1cf27b2a" u-r="activityRef" bindallLoadedChange="{{b}}" u-i="1cf27b2a-6" bind:__l="__l"/></view><view wx:if="{{c}}" class="no-more-divider data-v-1cf27b2a"><view class="no-more-line data-v-1cf27b2a"></view><text class="no-more-text data-v-1cf27b2a">没有更多了</text><view class="no-more-line data-v-1cf27b2a"></view></view></scroll-view><custom-tab-bar wx:if="{{e}}" class="data-v-1cf27b2a" u-i="1cf27b2a-7" bind:__l="__l" u-p="{{e}}"/><view class="fab-customer-service data-v-1cf27b2a" bindtap="{{g}}"><image class="fab-icon data-v-1cf27b2a" src="{{f}}" mode="aspectFit"></image></view><popup-ad-component wx:if="{{h}}" class="data-v-1cf27b2a" bindclose="{{i}}" u-i="1cf27b2a-8" bind:__l="__l" u-p="{{j}}"/></view>