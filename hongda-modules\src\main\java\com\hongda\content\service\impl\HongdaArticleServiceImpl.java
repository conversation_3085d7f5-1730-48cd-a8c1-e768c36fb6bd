package com.hongda.content.service.impl;

import com.hongda.common.core.service.OssFileStorageService;
import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.content.domain.HongdaArticle;
import com.hongda.content.domain.HongdaArticleTagRelation;
import com.hongda.content.mapper.HongdaArticleMapper;
import com.hongda.content.service.IHongdaArticleService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 资讯文章Service业务层处理
 * (最终修复版)
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaArticleServiceImpl implements IHongdaArticleService {
    @Autowired
    private HongdaArticleMapper hongdaArticleMapper;

    @Autowired
    private OssFileStorageService ossFileStorageService;

    // ... 其他所有 select, insert, update, delete 等方法保持不变 ...

    @Override
    public HongdaArticle selectHongdaArticleById(Long id) {
        HongdaArticle article = hongdaArticleMapper.selectHongdaArticleById(id);
        if (article != null) {
            article.setTagIds(hongdaArticleMapper.selectTagIdsByArticleId(id));
            article.setContent(processHtmlOnLoad(article.getContent()));
        }
        return article;
    }

    @Override
    public List<HongdaArticle> selectHongdaArticleList(HongdaArticle hongdaArticle) {
        List<HongdaArticle> list = hongdaArticleMapper.selectHongdaArticleList(hongdaArticle);
        list.forEach(article -> article.setContent(processHtmlOnLoad(article.getContent())));
        return list;
    }

    @Override
    @Transactional
    public int insertHongdaArticle(HongdaArticle hongdaArticle) {
        hongdaArticle.setCreateTime(DateUtils.getNowDate());
        hongdaArticle.setContent(processHtmlOnSave(hongdaArticle.getContent()));
        int rows = hongdaArticleMapper.insertHongdaArticle(hongdaArticle);
        insertArticleTag(hongdaArticle);
        return rows;
    }

    @Override
    @Transactional
    public int updateHongdaArticle(HongdaArticle hongdaArticle) {
        hongdaArticle.setUpdateTime(DateUtils.getNowDate());
        hongdaArticle.setContent(processHtmlOnSave(hongdaArticle.getContent()));
        hongdaArticleMapper.deleteArticleTagByArticleId(hongdaArticle.getId());
        insertArticleTag(hongdaArticle);
        return hongdaArticleMapper.updateHongdaArticle(hongdaArticle);
    }

    private void insertArticleTag(HongdaArticle hongdaArticle) {
        List<Long> tagIds = hongdaArticle.getTagIds();
        if (tagIds != null && !tagIds.isEmpty()) {
            List<HongdaArticleTagRelation> list = new ArrayList<>();
            for (Long tagId : tagIds) {
                HongdaArticleTagRelation relation = new HongdaArticleTagRelation();
                relation.setArticleId(hongdaArticle.getId());
                relation.setTagId(tagId);
                list.add(relation);
            }
            if (!list.isEmpty()) {
                hongdaArticleMapper.batchArticleTag(list);
            }
        }
    }

    @Override
    @Transactional
    public int deleteHongdaArticleByIds(Long[] ids) {
        for (Long id : ids) {
            hongdaArticleMapper.deleteArticleTagByArticleId(id);
        }
        return hongdaArticleMapper.deleteHongdaArticleByIds(ids);
    }

    @Override
    @Transactional
    public int deleteHongdaArticleById(Long id) {
        hongdaArticleMapper.deleteArticleTagByArticleId(id);
        return hongdaArticleMapper.deleteHongdaArticleById(id);
    }

    @Override
    public void incrementArticleViewCount(Long articleId) {
        if (articleId != null && articleId > 0) {
            hongdaArticleMapper.incrementViewCount(articleId);
        }
    }

    @Override
    public List<HongdaArticle> selectPublishedArticleListForMiniProgram(HongdaArticle hongdaArticle) {
        List<HongdaArticle> list = hongdaArticleMapper.selectPublishedArticleListForMiniProgram(hongdaArticle);
        list.forEach(article -> article.setContent(processHtmlOnLoad(article.getContent())));
        return list;
    }

    @Override
    public HongdaArticle selectPublishedArticleByIdForMiniProgram(Long id) {
        HongdaArticle article = hongdaArticleMapper.selectPublishedArticleByIdForMiniProgram(id);
        if (article != null) {
            article.setContent(processHtmlOnLoad(article.getContent()));
        }
        return article;
    }


    /**
     * [最终修改版] 在内容保存到数据库前进行处理
     * @param htmlContent 从前端接收到的、包含带 data-href 的 img 标签的HTML
     * @return 清理后的、只包含带 data-oss-object-name 的 img 标签的HTML
     */
    private String processHtmlOnSave(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }
        Document doc = Jsoup.parseBodyFragment(htmlContent);

        // 关键：选取所有 data-href 属性以 'oss-object-name://' 开头的 img 标签
        Elements images = doc.select("img[data-href^=oss-object-name://]");

        for (Element img : images) {
            String dataHref = img.attr("data-href");
            // 从 data-href 中提取出 objectName
            String objectName = dataHref.substring("oss-object-name://".length());

            // 设置我们最终需要的 data-oss-object-name 属性
            img.attr("data-oss-object-name", objectName);
            // 移除临时的 src 和 data-href 属性
            img.removeAttr("src");
            img.removeAttr("data-href");
        }
        return doc.body().html();
    }

    /**
     * [无需修改] 在从数据库读取内容后，返回给前端前进行处理
     */
    private String processHtmlOnLoad(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }
        Document doc = Jsoup.parseBodyFragment(htmlContent);
        Elements images = doc.select("img[data-oss-object-name]");
        for (Element img : images) {
            String objectName = img.attr("data-oss-object-name");
            if (StringUtils.isNotEmpty(objectName)) {
                String signedUrl = ossFileStorageService.getSignedUrl(objectName, 15);
                img.attr("src", signedUrl);
            }
        }
        return doc.body().html();
    }
}