{"version": 3, "file": "BannerComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9CYW5uZXJDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view v-if=\"swiperList.length > 0\" class=\"banner-container\">\r\n    <up-swiper\r\n        :list=\"swiperList\"\r\n        keyName=\"url\"\r\n        :circular=\"true\"\r\n        :autoplay=\"true\"\r\n        :interval=\"3000\"\r\n        :duration=\"500\"\r\n        :indicator=\"true\"\r\n        indicatorActiveColor=\"#FFFFFF\"\r\n        indicatorInactiveColor=\"rgba(255, 255, 255, 0.5)\"\r\n\r\n        indicatorMode=\"dot\"\r\n\r\n        @click=\"handleBannerClick\"\r\n        imgMode=\"aspectFill\"\r\n        height=\"296rpx\"\r\n        radius=\"16rpx\"\r\n    ></up-swiper>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\n// 步骤 1: 导入正确的广告 API\r\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\r\nimport { navigateTo } from '@/utils/navigation.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\n\r\nconst bannerList = ref([]);\r\n\r\n// 计算属性，将从接口获取的 imageUrl 转换为完整的图片路径\r\nconst swiperList = computed(() =>\r\n    bannerList.value.map(item => ({\r\n      ...item,\r\n      // 步骤 3: 使用广告表正确的图片字段名 imageUrl\r\n      url: getFullImageUrl(item.imageUrl)\r\n    }))\r\n);\r\n\r\n// 从接口获取轮播图数据\r\nconst fetchBannerData = async () => {\r\n  try {\r\n    // 步骤 2: 调用广告接口，并传入位置代码 'HOME_BANNER'\r\n    const res = await getAdListByPositionApi('HOME_BANNER');\r\n    if (res.data) {\r\n      bannerList.value = res.data;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取轮播图失败:', error);\r\n  }\r\n};\r\n\r\n// 点击轮播图项的事件处理\r\nconst handleBannerClick = (index) => {\r\n  const bannerItem = bannerList.value[index];\r\n  if (bannerItem) {\r\n    // navigateTo 工具函数会处理跳转逻辑\r\n    navigateTo(bannerItem);\r\n  }\r\n};\r\n\r\n// onMounted 生命周期钩子，在组件挂载后执行数据获取\r\nonMounted(() => {\r\n  fetchBannerData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.banner-container {\r\n  /* 容器与页面边缘的间距 */\r\n  margin: 24rpx 24rpx;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n\r\n  /* 容器高度，与 up-swiper 的 height 属性保持一致，确保占位正确 */\r\n  height: 296rpx;\r\n\r\n  /* 在图片加载出来前的背景色，防止空白 */\r\n  background-color: #f0f2f5;\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "computed", "getFullImageUrl", "getAdListByPositionApi", "uni", "navigateTo", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;AA8BA,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AAGzB,UAAM,aAAaC,cAAAA;AAAAA,MAAS,MACxB,WAAW,MAAM,IAAI,WAAS;AAAA,QAC5B,GAAG;AAAA;AAAA,QAEH,KAAKC,YAAAA,gBAAgB,KAAK,QAAQ;AAAA,MACxC,EAAM;AAAA,IACN;AAGA,UAAM,kBAAkB,YAAY;AAClC,UAAI;AAEF,cAAM,MAAM,MAAMC,uCAAuB,aAAa;AACtD,YAAI,IAAI,MAAM;AACZ,qBAAW,QAAQ,IAAI;AAAA,QACxB;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,6CAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,UAAU;AACnC,YAAM,aAAa,WAAW,MAAM,KAAK;AACzC,UAAI,YAAY;AAEdC,yBAAU,WAAC,UAAU;AAAA,MACtB;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;ACjED,GAAG,gBAAgB,SAAS;"}