{"version": 3, "file": "PopupAdComponent.js", "sources": ["components/common/PopupAdComponent.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvY29tbW9uL1BvcHVwQWRDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view v-if=\"visible\" class=\"popup-overlay\" @touchmove.prevent>\r\n    <view class=\"popup-content\">\r\n      <image\r\n          :src=\"adData.imageUrl\"\r\n          class=\"popup-image\"\r\n          mode=\"aspectFill\"\r\n          @click=\"handleAdClick\"\r\n      />\r\n      <view class=\"close-button\" @click=\"closePopup\">\r\n        <uni-icons type=\"closeempty\" size=\"20\" color=\"#fff\"></uni-icons>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch, computed } from 'vue';\r\n// 导入我们统一的导航工具函数\r\nimport { navigateTo } from '@/utils/navigation.js';\r\n\r\n// 定义 props，用于从父组件接收广告数据和显示状态\r\nconst props = defineProps({\r\n  show: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  adData: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n});\r\n\r\n// 定义 emit 事件，用于通知父组件关闭弹窗\r\nconst emit = defineEmits(['close']);\r\n\r\nconst visible = ref(props.show);\r\n\r\n// 监听父组件传入的 show 属性变化，同步控制弹窗的显示/隐藏\r\nwatch(() => props.show, (newVal) => {\r\n  console.log('弹窗显示状态变化:', newVal);\r\n  console.log('广告数据:', props.adData);\r\n  visible.value = newVal;\r\n}, { immediate: true });\r\n\r\n// 计算属性确保数据可用\r\nconst adData = computed(() => {\r\n  return props.adData || {};\r\n});\r\n\r\n// 处理广告图片的点击事件\r\nconst handleAdClick = () => {\r\n  console.log('广告被点击:', props.adData);\r\n  // 直接调用 navigateTo 函数，它能智能处理 linkType 和 linkTarget\r\n  if (props.adData && typeof navigateTo === 'function') {\r\n    navigateTo(props.adData);\r\n  }\r\n  // 点击后也关闭弹窗\r\n  closePopup();\r\n};\r\n\r\n// 关闭弹窗的函数\r\nconst closePopup = () => {\r\n  console.log('关闭弹窗');\r\n  visible.value = false;\r\n  emit('close'); // 通知父组件更新状态\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.popup-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.popup-content {\r\n  position: relative;\r\n  /* 按照设计稿的尺寸设置 */\r\n  width: 506rpx;\r\n  height: 900rpx;\r\n  border-radius: 0rpx;\r\n  overflow: visible; /* 改为 visible，让关闭按钮能显示在外面 */\r\n  background-color: #fff;\r\n}\r\n\r\n.popup-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 0rpx;\r\n  display: block;\r\n}\r\n\r\n.close-button {\r\n  position: absolute;\r\n  /* 将关闭按钮放在图片正下方 */\r\n  bottom: -100rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.close-button:active {\r\n  opacity: 0.7;\r\n  transform: scale(0.95);\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/common/PopupAdComponent.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "uni", "computed", "navigateTo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,UAAM,QAAQ;AAYd,UAAM,OAAO;AAEb,UAAM,UAAUA,cAAG,IAAC,MAAM,IAAI;AAG9BC,kBAAK,MAAC,MAAM,MAAM,MAAM,CAAC,WAAW;AAClCC,uFAAY,aAAa,MAAM;AAC/BA,oBAAY,MAAA,MAAA,OAAA,gDAAA,SAAS,MAAM,MAAM;AACjC,cAAQ,QAAQ;AAAA,IAClB,GAAG,EAAE,WAAW,KAAI,CAAE;AAGtB,UAAM,SAASC,cAAQ,SAAC,MAAM;AAC5B,aAAO,MAAM,UAAU;IACzB,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1BD,oBAAY,MAAA,MAAA,OAAA,gDAAA,UAAU,MAAM,MAAM;AAElC,UAAI,MAAM,UAAU,OAAOE,iBAAAA,eAAe,YAAY;AACpDA,oCAAW,MAAM,MAAM;AAAA,MACxB;AAED;IACF;AAGA,UAAM,aAAa,MAAM;AACvBF,oBAAAA,MAAY,MAAA,OAAA,gDAAA,MAAM;AAClB,cAAQ,QAAQ;AAChB,WAAK,OAAO;AAAA,IACd;;;;;;;;;;;;;;;;;;;;ACjEA,GAAG,gBAAgB,SAAS;"}