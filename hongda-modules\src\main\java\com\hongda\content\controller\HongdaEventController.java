package com.hongda.content.controller;

import java.util.List;

import com.hongda.content.domain.HongdaEvent;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.service.IHongdaEventService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 活动管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Tag(name = "活动管理", description = "活动信息管理相关接口")
@RestController
@RequestMapping("/content/event")
public class HongdaEventController extends BaseController
{
    @Autowired
    private IHongdaEventService hongdaEventService;

    /**
     * 查询活动管理列表
     */
    @Operation(summary = "查询活动列表", description = "分页查询活动管理列表")
    @PreAuthorize("@ss.hasPermi('content:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaEvent hongdaEvent)
    {
        startPage();
        List<HongdaEvent> list = hongdaEventService.selectHongdaEventList(hongdaEvent);
        return getDataTable(list);
    }

    /**
     * 导出活动管理列表
     */
    @Operation(summary = "导出活动列表", description = "导出活动管理列表到Excel文件")
    @PreAuthorize("@ss.hasPermi('content:event:export')")
    @Log(title = "活动管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaEvent hongdaEvent)
    {
        List<HongdaEvent> list = hongdaEventService.selectHongdaEventList(hongdaEvent);
        ExcelUtil<HongdaEvent> util = new ExcelUtil<HongdaEvent>(HongdaEvent.class);
        util.exportExcel(response, list, "活动管理数据");
    }

    /**
     * 获取活动管理详细信息
     */
    @Operation(summary = "获取活动详情", description = "根据活动ID获取活动详细信息")
    @PreAuthorize("@ss.hasPermi('content:event:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@Parameter(description = "活动ID", required = true) @PathVariable("id") Long id)
    {
        return success(hongdaEventService.selectHongdaEventById(id));
    }

    /**
     * 新增活动管理
     */
    @Operation(summary = "新增活动", description = "新增活动管理")
    @PreAuthorize("@ss.hasPermi('content:event:add')")
    @Log(title = "活动管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Parameter(description = "活动信息", required = true) @RequestBody HongdaEvent hongdaEvent)
    {
        System.out.println("新增活动请求 - 地址信息：" +
            "省份=" + hongdaEvent.getProvince() + 
            ", 城市=" + hongdaEvent.getCity() + 
            ", 区县=" + hongdaEvent.getDistrict() + 
            ", 详细地址=" + hongdaEvent.getAddressDetail());
            
        hongdaEventService.insertHongdaEvent(hongdaEvent);
        return AjaxResult.success(hongdaEvent); // 返回完整的对象
    }

    /**
     * 修改活动管理
     */
    @Operation(summary = "修改活动", description = "修改活动管理")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Parameter(description = "活动信息", required = true) @RequestBody HongdaEvent hongdaEvent)
    {
        System.out.println("修改活动请求 - 地址信息：" +
            "省份=" + hongdaEvent.getProvince() + 
            ", 城市=" + hongdaEvent.getCity() + 
            ", 区县=" + hongdaEvent.getDistrict() + 
            ", 详细地址=" + hongdaEvent.getAddressDetail());
            
        return toAjax(hongdaEventService.updateHongdaEvent(hongdaEvent));
    }

    /**
     * 删除活动管理
     */
    @Operation(summary = "删除活动", description = "批量删除活动管理")
    @PreAuthorize("@ss.hasPermi('content:event:remove')")
    @Log(title = "活动管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "活动ID数组", required = true) @PathVariable Long[] ids)
    {
        return toAjax(hongdaEventService.deleteHongdaEventByIds(ids));
    }

    /**
     * 修改活动热门状态
     */
    @Operation(summary = "修改活动热门状态", description = "修改活动的热门状态开关")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "活动热门状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeHotStatus")
    public AjaxResult changeHotStatus(@Parameter(description = "活动信息（包含ID和热门状态）", required = true) @RequestBody HongdaEvent hongdaEvent)
    {
        System.out.println("收到热门状态变更请求: ID=" + hongdaEvent.getId() + ", isHot=" + hongdaEvent.getIsHot());
        
        // 创建一个只包含必要字段的对象，避免更新其他字段
        HongdaEvent updateEvent = new HongdaEvent();
        updateEvent.setId(hongdaEvent.getId());
        updateEvent.setIsHot(hongdaEvent.getIsHot());
        
        int result = hongdaEventService.updateHongdaEvent(updateEvent);
        System.out.println("更新结果: " + result + " 行受影响");
        
        return toAjax(result);
    }

    /**
     * 手动触发活动状态自动更新（用于测试和管理）
     */
    @Operation(summary = "手动触发活动状态更新", description = "手动触发活动状态自动更新，用于测试定时任务功能")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "活动状态自动更新", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatusManually")
    public AjaxResult updateStatusManually()
    {
        try {
            int updatedCount = hongdaEventService.updateEventStatusAutomatically();
            return AjaxResult.success("状态更新完成", updatedCount);
        } catch (Exception e) {
            System.err.println("手动触发状态更新失败：" + e.getMessage());
            return AjaxResult.error("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 切换活动推广状态
     */
    @Operation(summary = "切换活动推广状态", description = "开启或关闭活动在首页的推广")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "活动推广状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changePromotionStatus")
    public AjaxResult changePromotionStatus(@Parameter(description = "推广配置数据", required = true) @RequestBody HongdaEvent promotionData) {
        try {
            int result = hongdaEventService.changeEventPromotionStatus(
                promotionData.getId(), promotionData);
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("推广状态切换失败：" + e.getMessage());
        }
    }

    /**
     * 取消活动推广
     */
    @Operation(summary = "取消活动推广", description = "取消活动的推广功能，清空所有推广相关数据")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "取消活动推广", businessType = BusinessType.UPDATE)
    @DeleteMapping("/promotion/{eventId}")
    public AjaxResult cancelPromotion(@Parameter(description = "活动ID", required = true) @PathVariable Long eventId) {
        try {
            int result = hongdaEventService.cancelEventPromotion(eventId);
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("取消推广失败：" + e.getMessage());
        }
    }

    /**
     * 获取推广活动列表（小程序前端使用）
     */
    @Operation(summary = "获取推广活动列表", description = "获取当前正在推广的活动列表，供小程序首页展示")
    @GetMapping("/promotion/list")
    public AjaxResult getPromotionEventList(
        @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
        @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize
    ) {
        try {
            startPage();
            List<HongdaEvent> promotionEvents = hongdaEventService.selectPromotionEventList();
            return AjaxResult.success(getDataTable(promotionEvents));
        } catch (Exception e) {
            return AjaxResult.error("获取推广活动列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据活动状态查询活动列表
     */
    @Operation(summary = "根据活动状态查询活动列表", description = "根据活动状态（0=未开始，1=进行中，2=已结束）查询活动列表")
    @GetMapping("/listByActivityStatus")
    public AjaxResult getEventListByActivityStatus(
        @Parameter(description = "活动状态", example = "1") @RequestParam Integer activityStatus,
        @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
        @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize
    ) {
        try {
            startPage();
            List<HongdaEvent> events = hongdaEventService.selectEventListByActivityStatus(activityStatus);
            return AjaxResult.success(getDataTable(events));
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据报名状态查询活动列表
     */
    @Operation(summary = "根据报名状态查询活动列表", description = "根据报名状态（0=未开始，1=报名中，2=已结束）查询活动列表")
    @GetMapping("/listByRegistrationStatus")
    public AjaxResult getEventListByRegistrationStatus(
        @Parameter(description = "报名状态", example = "1") @RequestParam Integer registrationStatus,
        @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
        @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize
    ) {
        try {
            startPage();
            List<HongdaEvent> events = hongdaEventService.selectEventListByRegistrationStatus(registrationStatus);
            return AjaxResult.success(getDataTable(events));
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取活动状态统计
     */
    @Operation(summary = "获取活动状态统计", description = "获取各种状态的活动数量统计")
    @GetMapping("/statistics")
    public AjaxResult getEventStatusStatistics() {
        try {
            return AjaxResult.success(hongdaEventService.getEventStatusStatistics());
        } catch (Exception e) {
            return AjaxResult.error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 查询即将开始的活动
     */
    @Operation(summary = "查询即将开始的活动", description = "查询1小时内即将开始的活动列表")
    @GetMapping("/upcoming")
    public AjaxResult getUpcomingEvents() {
        try {
            List<HongdaEvent> events = hongdaEventService.selectUpcomingEvents();
            return AjaxResult.success(events);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询报名即将截止的活动
     */
    @Operation(summary = "查询报名即将截止的活动", description = "查询24小时内报名即将截止的活动列表")
    @GetMapping("/registrationDeadline")
    public AjaxResult getRegistrationDeadlineEvents() {
        try {
            List<HongdaEvent> events = hongdaEventService.selectRegistrationDeadlineEvents();
            return AjaxResult.success(events);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 修改活动精选排序
     */
    @Operation(summary = "修改活动精选排序", description = "修改活动的精选排序值")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "活动精选排序", businessType = BusinessType.UPDATE)
    @PutMapping("/changeSortOrder")
    public AjaxResult changeSortOrder(@Parameter(description = "活动信息（包含ID和排序值）", required = true) @RequestBody HongdaEvent hongdaEvent) {
        // 参数校验
        if (hongdaEvent.getId() == null || hongdaEvent.getSortOrder() == null) {
            return AjaxResult.error("参数错误，活动ID和排序值不能为空");
        }
        
        try {
            // 调用Service层方法，只更新排序字段
            int result = hongdaEventService.updateEventSortOrder(hongdaEvent.getId(), hongdaEvent.getSortOrder());
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("排序更新失败：" + e.getMessage());
        }
    }


}
