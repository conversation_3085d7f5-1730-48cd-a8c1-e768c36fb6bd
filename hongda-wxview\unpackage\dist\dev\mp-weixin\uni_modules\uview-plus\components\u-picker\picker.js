"use strict";
const Picker = {
  // picker
  picker: {
    show: false,
    popupMode: "bottom",
    showToolbar: true,
    title: "",
    columns: [],
    loading: false,
    itemHeight: 44,
    cancelText: "取消",
    confirmText: "确定",
    cancelColor: "#909193",
    confirmColor: "",
    visibleItemCount: 5,
    keyName: "text",
    valueName: "value",
    closeOnClickOverlay: false,
    defaultIndex: [],
    immediateChange: true,
    zIndex: 10076,
    disabled: false,
    disabledColor: "",
    placeholder: "请选择",
    inputProps: {},
    bgColor: "",
    round: 0,
    duration: 300,
    overlayOpacity: 0.5
  }
};
exports.Picker = Picker;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uview-plus/components/u-picker/picker.js.map
