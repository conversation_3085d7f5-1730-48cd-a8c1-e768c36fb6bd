/**
 * 地点相关工具函数
 */

/**
 * 直辖市列表
 */
const MUNICIPALITIES = ['北京', '上海', '天津', '重庆'];

/**
 * 统一格式化活动地点显示
 * 对于直辖市，优先显示省字段；对于其他城市，优先显示市字段
 * @param {Object} event - 活动对象
 * @returns {string} 格式化后的地点
 */
export const formatEventLocation = (event) => {
  if (!event) return '待定';
  
  // 优先使用city字段
  if (event.city && event.city.trim()) {
    const city = event.city.trim();
    
    // 检查是否为直辖市
    const isMunicipality = MUNICIPALITIES.some(municipality => 
      city.includes(municipality)
    );
    
    if (isMunicipality) {
      // 对于直辖市，如果有province字段且包含直辖市名称，使用province
      if (event.province && event.province.trim()) {
        const province = event.province.trim();
        const municipalityMatch = MUNICIPALITIES.find(municipality => 
          province.includes(municipality)
        );
        if (municipalityMatch) {
          // 如果province字段已经包含"市"，直接返回；否则添加"市"
          return province.endsWith('市') ? province : `${municipalityMatch}市`;
        }
      }
      // 如果province字段不可用，从city字段中提取直辖市名称
      const municipalityMatch = MUNICIPALITIES.find(municipality => 
        city.includes(municipality)
      );
      if (municipalityMatch) {
        return city.endsWith('市') ? city : `${municipalityMatch}市`;
      }
    }
    
    // 对于非直辖市，直接返回city字段
    return city;
  }
  
  // 如果没有city字段，尝试使用province字段
  if (event.province && event.province.trim()) {
    const province = event.province.trim();
    
    // 检查province是否为直辖市
    const isMunicipality = MUNICIPALITIES.some(municipality => 
      province.includes(municipality)
    );
    
    if (isMunicipality) {
      const municipalityMatch = MUNICIPALITIES.find(municipality => 
        province.includes(municipality)
      );
      return province.endsWith('市') ? province : `${municipalityMatch}市`;
    }
    
    return province;
  }
  
  // 如果都没有，尝试从location字段提取
  if (event.location && event.location.trim()) {
    const location = event.location.trim();
    
    // 简单的城市提取逻辑
    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);
    if (match) {
      const province = match[1];
      const city = match[2];
      
      // 检查是否为直辖市
      const isProvinceMunicipality = MUNICIPALITIES.some(municipality => 
        province.includes(municipality)
      );
      
      if (isProvinceMunicipality) {
        const municipalityMatch = MUNICIPALITIES.find(municipality => 
          province.includes(municipality)
        );
        return province.endsWith('市') ? province : `${municipalityMatch}市`;
      }
      
      if (city && province !== city) {
        return city;
      }
      return province;
    }
    
    return location;
  }
  
  return '待定';
};

/**
 * 活动地点格式化函数（兼容旧的函数名）
 * @param {Object} item - 活动对象
 * @returns {string} 格式化后的地点
 */
export const formatActivityLocation = (item) => {
  return formatEventLocation(item);
};
