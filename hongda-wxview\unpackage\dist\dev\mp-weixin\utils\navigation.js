"use strict";
const common_vendor = require("../common/vendor.js");
function navigateTo(navItem) {
  if (!navItem || !navItem.linkType || !navItem.linkTarget) {
    common_vendor.index.__f__("warn", "at utils/navigation.js:9", "无效的导航项，缺少 linkType 或 linkTarget:", navItem);
    return;
  }
  const { linkType, linkTarget, title } = navItem;
  switch (linkType) {
    case "INTERNAL_PAGE":
      common_vendor.index.navigateTo({
        url: linkTarget,
        fail: (err) => common_vendor.index.__f__("error", "at utils/navigation.js:20", `跳转普通页面失败: ${linkTarget}`, err)
      });
      break;
    case "TAB_PAGE":
      common_vendor.index.switchTab({
        url: linkTarget,
        fail: (err) => common_vendor.index.__f__("error", "at utils/navigation.js:27", `跳转 TabBar 失败: ${linkTarget}`, err)
      });
      break;
    case "EXTERNAL_LINK":
      common_vendor.index.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(linkTarget)}&title=${title || ""}`,
        fail: (err) => common_vendor.index.__f__("error", "at utils/navigation.js:35", `跳转 Webview 失败: ${linkTarget}`, err)
      });
      break;
    default:
      common_vendor.index.__f__("warn", "at utils/navigation.js:40", "未知的链接类型:", linkType);
      break;
  }
}
exports.navigateTo = navigateTo;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/navigation.js.map
